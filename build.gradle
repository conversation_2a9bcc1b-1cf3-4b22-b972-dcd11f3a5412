// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        // Make sure that you have the following two repositories
        google()  // Google's Maven repository
        mavenCentral()  // Maven Central repository
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.5'
    }
}

plugins {
    id 'com.android.application' version '8.9.0' apply false
    id 'com.android.library' version '8.9.0' apply false
    id 'org.jetbrains.kotlin.android' version '1.7.10' apply false
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin' version '2.0.1' apply false
}

ext {
    clientName = "Aspire"
    applicationId = "com.tf.aspire"
    compileSdkVersion = 33
    buildToolsVersion = "29.0.3"
    minSdkVersion = 21
    targetSdkVersion = 33
    versionCode = 103
    versionName = "1.0.3"
}

task clean(type: Delete) {
    delete rootProject.buildDir
}