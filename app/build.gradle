plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-android-extensions'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'com.google.android.libraries.mapsplatform.secrets-gradle-plugin'
}

String getBuildVersionTime() {
    return "\"$rootProject.ext.versionName($rootProject.ext.versionCode) " + new Date().format("MM-dd:hh:mm") + "\""
}

String getApiEnv() {
    def env = 'prod'

    if (project.hasProperty('api_env')) {
        env = project.api_env
    }
    println "env:" + env
    return env
}

String getAppChannel() {
    def channel = 'googleplay'

    if (project.hasProperty('app_channel')) {
        channel = project.app_channel
    }
    println "channel:" + channel
    return channel
}

android {
    namespace 'com.fascin.chatter'
    compileSdk rootProject.ext.compileSdkVersion

    defaultConfig {
        applicationId rootProject.ext.applicationId
        minSdk rootProject.ext.minSdkVersion
        targetSdk rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        multiDexEnabled true
        ndk {
            abiFilters 'armeabi', 'arm64-v8a', 'armeabi-v7a'
        }
    }

    signingConfigs {
        config {
            storeFile file("../chatter")
            storePassword "chatter"
            keyAlias "chatter"
            keyPassword "chatter"
            v2SigningEnabled true // android 5.0-8.0，必须用V2，否则会有安全问题
            v1SigningEnabled true
        }
    }

    buildTypes {
        release {
            zipAlignEnabled true
            shrinkResources true
            minifyEnabled true
            signingConfig signingConfigs.config
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "CLIENT_NAME", "\"$rootProject.ext.clientName\""
            buildConfigField("String", "BUILD_VERSION", getBuildVersionTime())
            buildConfigField("String", "API_ENV", "\"${getApiEnv()}\"")
            buildConfigField("String", "APP_CHANNEL", "\"${getAppChannel()}\"")
            manifestPlaceholders["firebaseCrashlyticsCollectionEnabled"] = true
        }

        debug {
            minifyEnabled false
            signingConfig signingConfigs.config
            buildConfigField "String", "CLIENT_NAME", "\"$rootProject.ext.clientName\""
            buildConfigField("String", "BUILD_VERSION", getBuildVersionTime())
            buildConfigField("String", "API_ENV", "\"${getApiEnv()}\"")
            buildConfigField("String", "APP_CHANNEL", "\"${getAppChannel()}\"")
            manifestPlaceholders["firebaseCrashlyticsCollectionEnabled"] = false
        }

        profile {
            initWith release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    buildFeatures {
        viewBinding true
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    // 配置 APK 输出文件名
    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            outputFileName = "chattertool_${getApiEnv()}_${getBuildVersionTime()}.apk"
        }
    }

}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':lib_common')
    implementation project(':lib_basecore')
    implementation project(':lib_thirdparty')
    implementation project(':lib_gift')
    implementation project(':imkit')

    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.4.1'
    implementation 'androidx.navigation:navigation-ui-ktx:2.4.1'
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:32.1.0')
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.android.gms:play-services-auth:20.5.0'
    implementation 'com.ethanhua:skeleton:1.1.2'
    //主要是动画的实现
    implementation 'io.supercharge:shimmerlayout:2.1.0'

    implementation 'com.airbnb.android:lottie:5.2.0'
    implementation 'com.danikula:videocache:2.7.1'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'

    implementation "com.jude:rollviewpager:1.3.4"
    //GoogleMap
    implementation 'com.google.android.gms:play-services-maps:18.1.0'

    implementation 'com.daimajia.androidanimations:library:2.4@aar'
    implementation 'com.github.zhpanvip:bannerviewpager:3.5.12'
    implementation 'com.google.android.gms:play-services-location:19.0.1'
}