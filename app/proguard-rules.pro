# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile


#指定代码的压缩级别
-optimizationpasses 5

#包明不混合大小写
-dontusemixedcaseclassnames

#不去忽略非公共的库类
-dontskipnonpubliclibraryclasses

 #优化  不优化输入的类文件
-dontoptimize
-dontobfuscate
#-dontshrink

 #预校验
#-dontpreverify

 #混淆时是否记录日志
-verbose

 # 混淆时所采用的算法
-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod

-keepattributes Signature
-keep class com.google.gson.stream.* { *; }
-keep class com.google.gson.examples.android.model.* { *; }
-dontwarn com.google.gson.**
-keep class com.google.gson.* { *;}
-keep class org.json.* {*;}

-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

-keep public class * implements java.io.Serializable {*;}

-keepnames class * implements android.os.Parcelable {
    public static final ** CREATOR;
}
#如果引用了v4或者v7包
-dontwarn android.support.**
-keep public class * extends android.app.Activity
-keep public class * extends androidx.appcompat.app.AppCompatActivity
-keep public class * extends androidx.fragment.app.DialogFragment
-keep public class * extends androidx.fragment.app.Fragment
-keep public class * extends androidx.fragment.app.FragmentActivity


-dontwarn android.net.**
-keep class android.net.SSLCertificateSocketFactory{*;}
#如果引用了v4或者v7包
-dontwarn android.support.**

####混淆保护自己项目的部分代码以及引用的第三方jar包library-end####

#保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}

#保持自定义控件类不被混淆
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
}

#保持自定义控件类不被混淆
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...);
}


-keep public class * extends androidx.appcompat.app.AppCompatActivity {

}

#保持 Parcelable 不被混淆
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

#保持 Serializable 不被混淆
-keepnames class * implements java.io.Serializable

#保持 Serializable 不被混淆并且enum 类也不被混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

#保持枚举 enum 类不被混淆
-keepclassmembers enum * {
  public static **[] values();
  public static ** valueOf(java.lang.String);
}

-keepclassmembers class * {
    public void *ButtonClicked(android.view.View);
}

#不混淆资源类，保持R文件不被混淆，否则，你的反射是获取不到资源id的
-keepclassmembers class **.R$* {
    public static <fields>;
}

# Keep native methods
-keepclassmembers class * {
    native <methods>;
}

-dontwarn okio.**
-dontwarn com.squareup.okhttp.**
-dontwarn okhttp3.**
-dontwarn javax.annotation.**
-dontwarn com.android.volley.toolbox.**

#jsonwebtoken
-dontwarn javax.xml.bind.DatatypeConverter
-dontwarn org.bouncycastle.**
-keep class io.jsonwebtoken.** {*;}
-keepnames class com.fasterxml.jackson.** {*;}
-keepnames interface com.fasterxml.jackson.** {*;}

-dontwarn com.fasterxml.jackson.databind.**

#alibaba fastjson
-dontwarn com.alibaba.fastjson.**
-keep class com.alibaba.fastjson.** { *; }
-keepattributes Signature
-keepattributes *Annotation*

-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable

#firebase
-keep class com.google.firebase.** { *; }
-dontwarn org.apache.**

#google protobuf
-keep public class * extends com.google.protobuf.GeneratedMessage { *; }
-dontwarn sun.misc.Unsafe

#AppsFlyer uninstall tracking
-dontwarn com.android.installreferrer
-dontwarn com.appsflyer.**
-keep public class com.google.firebase.iid.FirebaseInstanceId {
    public *;
}

-keep public class com.google.android.gms.* { public *; }
-dontwarn com.google.android.gms.**


-keepnames class * extends android.view.View

-keep class * extends android.app.Fragment {
 public void setUserVisibleHint(boolean);
 public void onHiddenChanged(boolean);
 public void onResume();
 public void onPause();
}


# google ad
-keep public class com.google.android.gms.ads.** {
   public *;
}

-keep public class com.google.ads.** {
   public *;
}
-keep class com.google.obf.** { *; }
-keep interface com.google.obf.** { *; }

-keep class com.google.ads.interactivemedia.** { *; }
-keep interface com.google.ads.interactivemedia.** { *; }

# output mapping
-printmapping build/outputs/mapping/release/mapping.txt
# Retrofit

# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on RoboVM on iOS. Will not be used at runtime.
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
# Retain generic type information for use by reflection by converters and adapters.
-keepattributes Signature
# Retain declared checked exceptions for use by a Proxy instance.
-keepattributes Exceptions


### umeng
-keepclassmembers class * {
   public <init> (org.json.JSONObject);
}

-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable

#kotlin
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn kotlin.**
-keepclassmembers class **$WhenMappings {
    <fields>;
}
-keepclassmembers class kotlin.Metadata {
    public <methods>;
}
-assumenosideeffects class kotlin.jvm.internal.Intrinsics {
    static void checkParameterIsNotNull(java.lang.Object, java.lang.String);
}

# ServiceLoader support
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.android.AndroidDispatcherFactory {}

# Most of volatile fields are updated with AFU and should not be mangled
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

-keepnames class * extends android.view.View
-keepnames class * extends android.app.Fragment
-keepnames class * extends androidx.fragment.app.Fragment
-keep class android.support.v4.view.ViewPager$** {
  *;
}
-keep class androidx.viewpager.widget.ViewPager {
  *;
}
-keep class androidx.viewpager.widget.ViewPager$** {
  *;
}

# 如果使用了Gson之类的工具要使被它解析的JavaBean类即实体类不被混淆。
##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }

# App Data Beans
-keep class com.fascin.chatter.bean.** {*;}
-keep class com.fascin.chatter.im.msg.** {*;}
-keep class com.fascin.chatter.im.view.** {*;}
-keep class com.fascin.chatter.im.widgets.** {*;}

-keep class * extends com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView{ *; }
-keep class * extends com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo{*;}
-keep class com.iandroid.allclass.lib_basecore.view.recyclerview.** {*;}
-keep class com.iandroid.allclass.lib_basecore.toast.** {*;}
-keep class com.iandroid.allclass.lib_common.beans.** {*;}
-keep class com.iandroid.allclass.lib_common.network.** {*;}
-keep class com.iandroid.allclass.lib_common.utils.** {*;}
-keep class com.iandroid.allclass.lib_common.event.** {*;}
-keep class com.iandroid.allclass.lib_thirdparty.beans.** {*;}
-keep class com.iandroid.allclass.lib_common.route.bean.** {*;}
-keep class com.iandroid.allclass.lib_common.download.** {*;}
-keep class com.google.gson.** {*;}
-keep class com.squareup.retrofit2.**{*;}
-keep class com.jakewharton.retrofit.**{*;}
-keep class com.squareup.okhttp3.**{*;}


-keep,allowobfuscation,allowshrinking class io.reactivex.Flowable
-keep,allowobfuscation,allowshrinking class io.reactivex.Maybe
-keep,allowobfuscation,allowshrinking class io.reactivex.Observable
-keep,allowobfuscation,allowshrinking class io.reactivex.Single

################ ViewBinding & DataBinding ###############
-keepclassmembers class * implements android.viewbinding.ViewBinding {
  public static * inflate(android.view.LayoutInflater);
  public static * inflate(android.view.LayoutInflater, android.view.ViewGroup, boolean);
  public static * bind(android.view.View);
}

# @Keep
-keep,allowobfuscation @interface androidx.annotation.Keep

-keep @androidx.annotation.Keep class *
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

-dontwarn com.netease.**
-keep class com.netease.** {*;}

#mmfile
# 保留本地native方法不被混淆
-keepclasseswithmembernames class * {
native <methods>;
}
-keep class com.google.gson.** {*;}
# Retain generic signatures of TypeToken and its subclasses with R8 version 3.0 and higher.
-keep,allowobfuscation,allowshrinking class com.google.gson.reflect.TypeToken
-keep,allowobfuscation,allowshrinking class * extends com.google.gson.reflect.TypeToken

#DrawerLayout反射
-keepclasseswithmembernames class androidx.drawerlayout.widget.DrawerLayout{
    <fields>;
}

-keepclasseswithmembernames class androidx.customview.widget.ViewDragHelper{
    <fields>;
}

#firebase
-keep class com.google.firebase.** { *; }
-dontwarn org.apache.**

-keepattributes EnclosingMethod
-keepattributes InnerClasses

#Glide webp
-keep public class com.bumptech.glide.integration.webp.WebpImage { *; }
-keep public class com.bumptech.glide.integration.webp.WebpFrame { *; }
-keep public class com.bumptech.glide.integration.webp.WebpBitmapFactory { *; }
#-keep public class com.bumptech.glide.load.resource.gif.GifDrawable { *; }
#-keep class com.bumptech.glide.load.resource.gif.GifDrawable { *; }
#-keep public class com.bumptech.glide.load.resource.gif.GifFrameLoader { *; }
#-keep public class com.bumptech.glide.integration.webp.decoder.WebpDrawable { *; }
-keep class com.bumptech.glide.load.resource.gif.**{*;}
-keep class com.bumptech.glide.integration.webp.decoder.**{*;}
-dontwarn com.bumptech.glide.**
-keep class com.bumptech.glide.**{*;}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
 -keep public class * implements com.bumptech.glide.module.GlideModule
 -keep public enum com.bumptech.glide.load.resource.bitmap.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}

-keepattributes Exceptions,InnerClasses

-keepattributes Signature

-keep class io.rong.** {*;}
-keep class cn.rongcloud.** {*;}
-keep class * implements io.rong.imlib.model.MessageContent {*;}
-dontwarn io.rong.push.**
-dontnote com.xiaomi.**
-dontnote com.google.android.gms.gcm.**
-dontnote io.rong.**

# 下方混淆使用了融云 IMKit 提供的 locationKit 位置插件时才需要配置，可参考高德官网的混淆方式:https://lbs.amap.com/api/android-sdk/guide/create-project/dev-attention
-keep class com.amap.api.maps.**{*;}
-keep class com.autonavi.**{*;}
-keep class com.amap.api.trace.**{*;}
-keep class com.amap.api.location.**{*;}
-keep class com.amap.api.fence.**{*;}
-keep class com.loc.**{*;}
-keep class com.autonavi.aps.amapapi.model.**{*;}
-keep class com.amap.api.services.**{*;}

-ignorewarnings



# Gson specific classes
-keep class sun.misc.Unsafe { *; }
#-keep class com.google.gson.stream.** { *; }
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
#-keep class com.google.gson.stream.** { *; }
#不混淆Annotation
-keepattributes Annotation,InnerClasses
#不混淆泛型
-keepattributes Signature
#抛出异常时保留代码行号
-keepattributes SourceFile,LineNumberTable

-keepclassmembers public class * implements java.io.Serializable{
    private *;
   void set*(***);
   *** get*();
}

# LitePal数据库
-keep class org.litepal.** {*;}
-keep class * extends org.litepal.crud.LitePalSupport {*;}

-keep class androidx.recyclerview.widget.**{*;}
-keep class androidx.viewpager2.widget.**{*;}