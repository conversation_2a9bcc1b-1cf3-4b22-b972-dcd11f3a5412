package com.fascin.chatter.main.chats

import android.util.Log
import com.iandroid.allclass.lib_common.AppController
import io.rong.imkit.IMCenter
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.ConversationIdentifier
import java.util.concurrent.ConcurrentHashMap

/**
 * @Desc: 置顶相关
 * @Created: Quan
 * @Date: 2024/4/16
 */
object ChatTopHelper {

    const val STATUS_SUCCESS = 0 // 置顶、取消置顶成功
    const val STATUS_ERROR = 1 // 置顶、取消置顶失败
    const val STATUS_STINT = 2 // 置顶数达到最大值，不能再置顶

    private var topMap: ConcurrentHashMap<String, ArrayList<Conversation?>> = ConcurrentHashMap()
    private val conversationTypes = arrayOf(Conversation.ConversationType.PRIVATE)

    /**
     * 获取置顶会话列表
     */
    fun resetTops(cleanTop: Boolean = false) {
        RongIMClient.getInstance().getTopConversationList(object : RongIMClient.ResultCallback<List<Conversation?>?>() {
            override fun onSuccess(tops: List<Conversation?>?) {
                Log.e("ChatTopHelper", "resetTops: ${tops?.size}")
                topMap.clear()
                if (tops?.isNotEmpty() == true) {
                    // 按照最后一次联系的时间来排序（tops默认顺序为置顶先后的顺序）
                    tops.sortedByDescending { it?.sentTime }.onEach {
                        if (ChatsConfig.allModelIdAndHead?.isNotEmpty() == true
                            && !ChatsConfig.allModelIdAndHead.containsKey(getModelId(it?.targetId))
                        ) {
                            clearModelDeletedConversationTop(it)
                        } else {
                            addTop(it)
                        }
                    }
                    if (cleanTop) cleanTop()
                }
            }

            override fun onError(e: RongIMClient.ErrorCode?) {
                Log.e("ChatTopHelper", "resetTops onError")
            }

        }, *conversationTypes)
    }

    /**
     * 清除超过最大数量的会话置顶状态
     */
    fun cleanTop() {
        var count = 0 // 已执行清除的数量
        var clearedCount = 0 // 要被执行清除的数量
        val topMaxSize: Int = AppController.getModelChatTopMax()
        topMap.forEach { (_, topList) ->
            val cleanTops = topList.filterIndexed { index, _ -> index >= topMaxSize }
            clearedCount += cleanTops.size
            if (cleanTops.isNotEmpty()) {
                cleanTops.forEach {
                    toTop(it, false) {
                        count++
                        // 如果全部clear完成，则重新获取置顶列表
                        if (count == clearedCount) resetTops()
                    }
                }
            }
        }
    }

    /**
     * 置顶或取消置顶
     * @param conversation 会话
     * @param refresh 是否需要手动处理置顶数据
     * @param block 回调
     */
    fun toTop(conversation: Conversation?, refresh: Boolean = false, block: (Int) -> Unit) {
        conversation?.let { con ->
            if (!canTop(con.targetId, con.isTop)) {
                // 超过最大置顶数量
                block.invoke(STATUS_STINT)
                return
            }
            IMCenter.getInstance()
                .setConversationToTop(
                    ConversationIdentifier(con.conversationType, con.targetId),
                    !con.isTop,
                    false,
                    object : RongIMClient.ResultCallback<Boolean?>() {
                        override fun onSuccess(isTop: Boolean?) {
                            if (refresh) {
                                if (con.isTop) {
                                    // 执行的是取消置顶操作
                                    removeTop(con.targetId)
                                } else {
                                    // 执行的是置顶操作
                                    addTop(con.also { it.isTop = false })
                                }
                            }
                            block.invoke(STATUS_SUCCESS)
                        }

                        override fun onError(
                            errorCode: RongIMClient.ErrorCode,
                        ) {
                            block.invoke(STATUS_ERROR)
                        }
                    })
        }
    }

    /**
     * 清除已删除model的置顶会话（不属于当前chatter的model的历史置顶）
     */
    private fun clearModelDeletedConversationTop(conversion: Conversation?) {
        conversion?.let { con ->
            IMCenter.getInstance()
                .setConversationToTop(
                    ConversationIdentifier(con.conversationType, con.targetId),
                    !con.isTop,
                    false, null
                )
        }
    }

    fun isTop(targetId: String): Boolean {
        return if (topMap.containsKey(getModelId(targetId))) {
            topMap[getModelId(targetId)]?.any { it?.targetId == targetId } == true
        } else {
            false
        }
    }

    /**
     * 是否可以置顶或取消置顶
     * @param isTop 当前是否已置顶
     */
    private fun canTop(targetId: String, isTop: Boolean): Boolean {
        val topMaxSize: Int = AppController.getModelChatTopMax()
        val topList = topMap[getModelId(targetId)] ?: ArrayList()
        return isTop || topList.size < topMaxSize
    }

    /**
     * 添加新置顶数据
     */
    private fun addTop(conversation: Conversation?) {
        conversation?.let {
            val modelId = getModelId(it.targetId)
            if (modelId.isNotEmpty()) {
                if (!topMap.containsKey(modelId)) {
                    topMap[modelId] = ArrayList()
                }
                topMap[modelId]?.add(it)
            }
        }
    }

    /**
     * 移除指定置顶
     */
    private fun removeTop(targetId: String) {
        val modelId = getModelId(targetId)
        if (modelId.isNotEmpty()) {
            if (topMap.containsKey(modelId)) {
                topMap[modelId]?.forEach {
                    if (it?.targetId == targetId) {
                        topMap[modelId]?.remove(it)
                        return
                    }
                }
            }
        }
    }

    private fun getModelId(imId: String?): String {
        if (!imId.isNullOrEmpty()) {
            val ids = imId.split("_")
            return if (ids.size > 1) ids[0] else ""
        }
        return ""
    }
}