package com.fascin.chatter.main.view

import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.FlashChatEntity
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.adapter.FlashChatModelAdapter
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.disableCopy
import com.iandroid.allclass.lib_common.utils.exts.isVisible
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.dialog_close
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.editCount
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.editInput
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.flashChatRv
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.interests
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.ivHead
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.llRoot
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.llTab
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.msgContinue
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.nickName
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.tvAge
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.userNearby
import kotlinx.android.synthetic.main.dialog_flash_chat_vie.userOnline


/**
 * @Desc: Chatter发起Flash chat
 * @Created: Quan
 * @Date: 2023/9/18
 */
class FlashChatVieDialog() : BaseDialogFragment() {

    private var pushData: FlashChatEntity? = null
    private var models: List<UserEntity>? = null
    private var adapter: FlashChatModelAdapter? = null
    private var viewModel: ProfileViewModel? = null
    private var selectModelId: String = ""

    constructor(pushData: FlashChatEntity, models: List<UserEntity>) : this() {
        this.pushData = pushData
        this.models = models
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_flash_chat_vie, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.859).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false
        isActiveDialogShow = true
        viewModel = ViewModelProvider(this).get(ProfileViewModel::class.java)
        pushData?.user?.let {
            ivHead.loadImageCircleCrop(AppContext.context, it.avatarUrl)
            nickName.text = it.nickname
            if (it.age > 0) tvAge.text = buildString {
                append(",")
                append(it.age)
            }
            userOnline.show(UserOnlineHelper.isOnline(it.userId))
            userNearby.show(it.address.isNotEmpty())
            userNearby.text = it.address.ifEmpty { "" }
            llTab.show(userOnline.isVisible() || it.address.isNotEmpty())
            interests.apply {
                setIsSingleLineWrapWidth(false)
                setDataSource(it.tags.orEmpty())
            }
        }
        adapter = FlashChatModelAdapter()
        flashChatRv.layoutManager = LinearLayoutManager(context, RecyclerView.HORIZONTAL, false)
        flashChatRv.adapter = adapter
        adapter?.updateData(models)
        setEditView()
        dialog_close.clickWithTrigger {
            dismissAllowingStateLoss()
        }

        msgContinue.clickWithTrigger {
            selectModelId = adapter?.selectId!!
            val msg = editInput.text?.trim().toString()
            KeyboardUtils.hideKeyboard(editInput)
            if (selectModelId.isNotEmpty() && pushData?.user?.userId?.isNotEmpty() == true && msg.isNotEmpty()) {
                msgContinue.setButtonStatus(SUButtonStatus.Loading)
                // flash chat
                viewModel?.sendFlashChatMsg(
                    pushData?.user?.userId,
                    selectModelId,
                    msg,
                    stamp = pushData?.stamp.toString(),
                    from = pushData?.user?.from
                )
            }
        }

        viewModel?.flashChatMsgResult?.observe(this) {
            if (it && selectModelId.isNotEmpty() && pushData?.user?.userId?.isNotEmpty() == true) {
                RouteUtils.routeToConversationActivity(
                    context,
                    Conversation.ConversationType.PRIVATE,
                    selectModelId + "_" + pushData?.user?.userId
                )
            }
            dismissAllowingStateLoss()
        }

        llRoot.setDispatchTouchEventListener {
            if (it.action == MotionEvent.ACTION_DOWN)
                AppModule.userActive()
        }
    }

    private fun setEditView() {
        msgContinue.setText(getString(R.string.flash_chat_continue))
        // 禁用edittext复制粘贴
        editInput.disableCopy()
        setEditUI()
        setContinueStatus()
        // 监听edit内容变化
        editInput.addTextChangedListener {
            setEditUI()
            setContinueStatus()
        }
    }

    private fun setEditUI() {
        // 初始化字数标识
        editCount.text = String.format(
            getString(R.string.message_setting_edit_count),
            editInput.text?.trim().toString().length
        )
    }

    /**
     * 设置按钮状态
     */
    private fun setContinueStatus() {
        if (msgContinue.getButtonStatus() != SUButtonStatus.Loading) {
            if (editInput.text?.trim().toString().length >= 10) msgContinue.setButtonStatus(
                SUButtonStatus.Activated
            )
            else msgContinue.setButtonStatus(
                SUButtonStatus.Disabled, R.drawable.bg_d9d9d9_r16,
                "#ffffff"
            )
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        KeyboardUtils.hideKeyboard(editInput)
        super.onDismiss(dialog)
        isActiveDialogShow = false
    }

    companion object {
        var isActiveDialogShow = false
    }
}