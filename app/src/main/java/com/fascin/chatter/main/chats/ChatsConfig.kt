package com.fascin.chatter.main.chats

object ChatsConfig {

    const val FlagMsgResetAll = -1
    const val FlagMsgFilter20 = 1

    //会员筛选
    const val FlagMemberAll = -1
    const val FlagMemberNoMember = 0
    const val FlagMemberMember = 1

    //解锁筛选
    const val FlagUnlockAll = -1
    const val FlagNonUnlock = 0
    const val FlagUnlock = 1

    //在线筛选
    const val FlagActiveAll = -1
    const val FlagActiveOffline = 0
    const val FlagActiveOnline = 1

    //等级筛选
    const val FlagLvlAll = -1
    const val FlagLvlLv1 = 1
    const val FlagLvlLv2 = 2
    const val FlagLvlLv3 = 3

    //New筛选
    const val FlagNewAll = -1
    const val FlagNonNew = 0
    const val FlagNew = 1

    // 主页Chat三个tab flag
    const val FlagTabMatch = 0
    const val FlagTabAllChats = 1
    const val FlagTabLongLost = 2

    // 筛选flag（默认选中All）
    var memberFlag: Int = FlagMemberAll
    var gradleFlag: Int = FlagLvlAll
    var activeFlag: Int = FlagActiveAll
    var unlockFlag: Int = FlagUnlockAll
    var newFlag: Int = FlagNewAll
    var msgFilterFlag: Int = FlagMsgResetAll

    // contact聊天标签
    const val TAG_NO_SEL = -100 // 筛选处未选择时的状态
    const val TAG_NONE = 0
    const val TAG_MONEY = 1
    const val TAG_HOT = 2
    const val TAG_HEART = 3
    const val TAG_TURTLE = 4

    // contact 聊天状态
    const val FlagUnknown = 0
    const val FlagReceived = 1
    const val FlagDelivered = 2
    const val FlagUnlocked = 3
    const val FlagMatched = 4
    const val FlagNewMsg = 5

    // 当前正在聊天的会话id
    var currentChatImId: String = ""
    // 点击首页质检提示，记录质检id和imid
    var chatPenaltyTag: String = ""

    // 当前所有的modelId和头像
    var allModelIdAndHead: HashMap<String, String> = HashMap()

    // 用于共享页面数据的view model
    var shareViewModel: ChatsViewModel? = null
}