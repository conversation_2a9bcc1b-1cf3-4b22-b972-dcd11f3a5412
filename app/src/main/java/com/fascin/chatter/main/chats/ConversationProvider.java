package com.fascin.chatter.main.chats;

import static android.content.Context.MODE_PRIVATE;
import static com.fascin.chatter.AppModuleKt.inAppOnlineSwitch;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.fascin.chatter.R;
import com.fascin.chatter.bean.chat.PrivacyUnlockDBEntity;
import com.fascin.chatter.bean.chat.UserImExtraEntity;
import com.fascin.chatter.component.views.ContactMsgTagView;
import com.fascin.chatter.component.views.ContactTagView;
import com.fascin.chatter.im.UserOnlineHelper;
import com.fascin.chatter.utils.SUDateUtils;
import com.fascin.chatter.utils.SUImActionItemExtKt;
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils;
import com.iandroid.allclass.lib_common.AppContext;
import com.iandroid.allclass.lib_common.utils.GsonUtils;
import com.iandroid.allclass.lib_common.views.VipImageView;

import java.util.List;

import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.conversationlist.model.BaseUiConversation;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imkit.utils.RongUtils;
import io.rong.imkit.widget.adapter.IViewProvider;
import io.rong.imkit.widget.adapter.IViewProviderListener;
import io.rong.imkit.widget.adapter.ViewHolder;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.UserInfo;

public class ConversationProvider implements IViewProvider<BaseUiConversation> {

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view =
                LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.layout_conversationlist_item, parent, false);
        return ViewHolder.createViewHolder(parent.getContext(), view);
    }

    @Override
    public boolean isItemViewType(BaseUiConversation item) {
        return true;
    }

    @Override
    public void bindViewHolder(
            final ViewHolder holder,
            final BaseUiConversation uiConversation,
            int position,
            List<BaseUiConversation> list,
            IViewProviderListener<BaseUiConversation> listener) {
        holder.setText(R.id.rc_conversation_title, uiConversation.mCore.getConversationTitle());

        // 会话头像
        if (!TextUtils.isEmpty(uiConversation.mCore.getPortraitUrl())) {
            RongConfigCenter.featureConfig()
                    .getKitImageEngine()
                    .loadCustomConversationListPortrait(
                            AppContext.context,
                            uiConversation.mCore.getPortraitUrl(),
                            holder.<ImageView>getView(R.id.rc_conversation_portrait),
                            uiConversation.mCore,
                            DeviceUtils.dp2px(holder.getContext(), 12));
        } else {
            int drawableId = com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_s;
            if (uiConversation
                    .mCore
                    .getConversationType()
                    .equals(Conversation.ConversationType.GROUP)) {
                drawableId = com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_s;
            } else if (uiConversation
                    .mCore
                    .getConversationType()
                    .equals(Conversation.ConversationType.CHATROOM)) {
                drawableId = com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_s;
            } else if (uiConversation
                    .mCore
                    .getConversationType()
                    .equals(Conversation.ConversationType.CUSTOMER_SERVICE)) {
                drawableId = com.iandroid.allclass.lib_common.R.drawable.bg_square_gray_s;
            }

            Uri uri = RongUtils.getUriFromDrawableRes(holder.getContext(), drawableId);
            RongConfigCenter.featureConfig()
                    .getKitImageEngine()
                    .loadCustomConversationListPortrait(
                            AppContext.context,
                            uri.toString(),
                            holder.getView(R.id.rc_conversation_portrait),
                            uiConversation.mCore,
                            DeviceUtils.dp2px(holder.getContext(), 12));
        }
        holder.getView(R.id.rc_conversation_portrait)
                .setOnClickListener(v -> {
                    if (RongConfigCenter.conversationListConfig().getListener()
                            != null) {
                        RongConfigCenter.conversationListConfig()
                                .getListener()
                                .onConversationPortraitClick(
                                        holder.getContext(),
                                        uiConversation.mCore.getConversationType(),
                                        uiConversation.mCore.getTargetId());
                    }
                });
        holder.getView(R.id.rc_conversation_portrait)
                .setOnLongClickListener(v -> {
                    if (RongConfigCenter.conversationListConfig().getListener()
                            != null) {
                        return RongConfigCenter.conversationListConfig()
                                .getListener()
                                .onConversationPortraitLongClick(
                                        holder.getContext(),
                                        uiConversation.mCore.getConversationType(),
                                        uiConversation.mCore.getTargetId());
                    }
                    return false;
                });
        // 设置最后消息的情况
        setMsgFlag(holder, uiConversation);
        // 未读数
        int unreadCount = uiConversation.mCore.getUnreadMessageCount();
        if (unreadCount > 0) {
            holder.setVisible(R.id.rc_conversation_unread_count, true);
            if (unreadCount > 99) {
                holder.setText(R.id.rc_conversation_unread_count, holder.getContext().getString(io.rong.imkit.R.string.rc_conversation_unread_dot));
            } else {
                String count = Integer.toString(unreadCount);
                holder.setText(R.id.rc_conversation_unread_count, count);
            }
        } else {
            holder.setVisible(R.id.rc_conversation_unread_count, false);
        }

        if (uiConversation.mCore.isTop()) {
            holder.getConvertView()
                    .setBackgroundColor(
                            holder.getContext().getResources().getColor(io.rong.imkit.R.color.rc_item_top_color));
        } else {
            holder.getConvertView().setBackgroundColor(holder.getContext().getResources()
                    .getColor(io.rong.imkit.R.color.rc_white_color));

        }

        UserInfo userInfo = RongUserInfoManager.getInstance().getUserInfo(uiConversation.mCore.getTargetId());
        UserImExtraEntity userImExtra = null;
        if (userInfo != null) {
            if (!"YourMatch".equals(userInfo.getName())) {
                holder.setText(R.id.rc_conversation_title, userInfo.getName());
            }
            if (TextUtils.isEmpty(uiConversation.mCore.getPortraitUrl())
                    && !TextUtils.isEmpty(userInfo.getPortraitUri().toString())) {
                uiConversation.mCore.setPortraitUrl(userInfo.getPortraitUri().toString());
                RongConfigCenter.featureConfig()
                        .getKitImageEngine()
                        .loadConversationListPortrait(
                                AppContext.context,
                                uiConversation.mCore.getPortraitUrl(),
                                holder.<ImageView>getView(R.id.rc_conversation_portrait),
                                uiConversation.mCore);
            }
            userImExtra = GsonUtils.INSTANCE.fromJson(userInfo.getExtra(), UserImExtraEntity.class);
            VipImageView vipImageView = holder.getView(R.id.rc_conversation_vip);
            ContactTagView contactTagView = holder.getView(R.id.ctConversationTag);
            if (userImExtra != null) {
                vipImageView.setVipStatus(userImExtra.getVip());
                contactTagView.setTagUI(uiConversation.mCore.getTargetId(), userImExtra.getContactTag(), userInfo.getName());
                setUnlockView(holder, uiConversation);
                if (inAppOnlineSwitch()) {
                    holder.setVisible(
                            R.id.rc_conversation_off_online,
                            SUImActionItemExtKt.isFlagEnabled(userImExtra.getChatFlag(), SUImActionItemExtKt.ConnectFlagNoOnline)
                    );
                } else {
                    holder.setVisible(R.id.rc_conversation_off_online, false);
                }
                holder.setVisible(R.id.llFlashChat, userImExtra.getFcCnt() == 1);
                holder.setVisible(R.id.tagPrime, userImExtra.getTagPrime() == 1);
                holder.setVisible(R.id.tagRising, userImExtra.getTagRising() == 1);
                holder.setVisible(R.id.rc_user_new, userImExtra.getNewIceBeak() == 1);
                boolean visible = SUImActionItemExtKt.isFlagEnabled(userImExtra.getChatFlag(), SUImActionItemExtKt.ConnectFlagInvisible) ||
                        SUImActionItemExtKt.isFlagEnabled(userImExtra.getChatFlag(), SUImActionItemExtKt.ConnectFlagProhibition);
                holder.setVisible(
                        R.id.rc_conversation_invisible,
                        visible
                );
                holder.setVisible(
                        R.id.rc_conversation_no_disturb,
                        SUImActionItemExtKt.isFlagEnabled(userImExtra.getChatFlag(), SUImActionItemExtKt.ConnectFlagMuteMsg)
                );
                holder.setBackgroundRes(R.id.rc_conversation_unread_count,
                        SUImActionItemExtKt.isFlagEnabled(userImExtra.getChatFlag(), SUImActionItemExtKt.ConnectFlagMuteMsg)
                                ? R.drawable.bg_unread_count_gray : R.drawable.bg_unread_count);
            } else {
                vipImageView.setVisibility(View.GONE);
                holder.setVisible(R.id.rc_user_new, false);
                holder.setHoldVisible(R.id.rc_conversation_off_online, false);
                holder.setHoldVisible(R.id.rc_conversation_invisible, false);
                holder.setHoldVisible(R.id.rc_conversation_no_disturb, false);
                holder.setBackgroundRes(R.id.rc_conversation_unread_count, R.drawable.bg_unread_count);
            }
        }
        // 设置在线状态
        holder.setVisible(
                R.id.rc_conversation_user_online,
                UserOnlineHelper.INSTANCE.isOnline(uiConversation.mCore.getTargetId()) || (userInfo != null && userImExtra != null &&
                        UserOnlineHelper.INSTANCE.isActive(userInfo.getUserId(), userImExtra.getLastActiveTime()))
        );
        // 设置在线状态图标
        if (UserOnlineHelper.INSTANCE.isOnline(uiConversation.mCore.getTargetId())) {
            holder.setBackgroundRes(R.id.rc_conversation_user_online, R.drawable.shape_user_online);
        } else {
            holder.setBackgroundRes(R.id.rc_conversation_user_online, R.drawable.shape_user_active);
        }

        String timeDifference = SUDateUtils.INSTANCE.getTimeDifference(uiConversation.mCore.getSentTime());
        holder.setText(R.id.rc_conversation_date, timeDifference);
        holder.setVisible(R.id.rc_conversation_date, !TextUtils.isEmpty(timeDifference));
    }

    private void setMsgFlag(ViewHolder holder, BaseUiConversation uiConversation) {
        ContactMsgTagView msgTag = holder.getView(R.id.ivChatTag);
        int flag = ChatsConfig.FlagUnknown;
        if (uiConversation.mCore.getUnreadMessageCount() > 0) {
            flag = ChatsConfig.FlagNewMsg;
        } else if (uiConversation.mCore.getLatestMessageDirection() == Message.MessageDirection.SEND) {
            flag = ChatsConfig.FlagDelivered;
        } else if (uiConversation.mCore.getLatestMessageDirection() == Message.MessageDirection.RECEIVE) {
            flag = ChatsConfig.FlagReceived;
        }
        if (uiConversation.unlockEntity instanceof PrivacyUnlockDBEntity) {
            PrivacyUnlockDBEntity unlockInfo = (PrivacyUnlockDBEntity) uiConversation.unlockEntity;
            if (unlockInfo.getHasNewUnlock() == 1) {
                flag = ChatsConfig.FlagUnlocked;
            }
        }
        msgTag.setChatFlag(flag);
    }

    private void setUnlockView(final ViewHolder holder, final BaseUiConversation uiConversation) {
        if (uiConversation.unlockEntity instanceof PrivacyUnlockDBEntity) {
            PrivacyUnlockDBEntity unlockInfo = (PrivacyUnlockDBEntity) uiConversation.unlockEntity;
            // 在这里处理耗时操作的结果
            holder.setText(R.id.tagUnlock,
                    AppContext.context.getString(R.string.conversation_tab_unlock) + " " + unlockInfo.getUnlockNum());
//            if (unlockInfo.getToDayUnlockPrivacyNum() > 0) {
//                holder.setBackgroundRes(R.id.ivLock, R.drawable.ic_contact_unluck_num);
//            } else {
//                holder.setBackgroundRes(R.id.ivLock, R.drawable.ic_contact_today_no_unlock);
//            }
            holder.setVisible(R.id.tagUnlock, unlockInfo.getUnlockNum() > 0);
        }
    }

    private boolean isDebugMode(Context context) {
        return context.getSharedPreferences("config", MODE_PRIVATE).getBoolean("isDebug", false);
    }
}
