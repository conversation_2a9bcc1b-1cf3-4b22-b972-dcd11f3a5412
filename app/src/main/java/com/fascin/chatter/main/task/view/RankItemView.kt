package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.RankEntity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import kotlinx.android.synthetic.main.itemview_rank.view.tvAmount
import kotlinx.android.synthetic.main.itemview_rank.view.tvName
import kotlinx.android.synthetic.main.itemview_rank.view.tvPPVCount
import kotlinx.android.synthetic.main.itemview_rank.view.tvRank

/**
 * @Desc: 排行榜 item
 * @Created: QuanZH
 * @Date: 2024/12/6
 */
@RvItem(id = AppViewType.rankItemView, spanCount = 1)
class RankItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_rank
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            if (UserController.isMe(entity.id)) {
                tvRank.textColorResource = com.iandroid.allclass.lib_basecore.R.color.cl_9370DB
                tvName.textColorResource = com.iandroid.allclass.lib_basecore.R.color.cl_9370DB
            }
            tvRank.text = entity.ranking.toString()
            tvName.text = entity.id
            tvAmount.show(entity.showReward == 1)
            tvPPVCount.text = entity.ppvCount.toString()
            tvAmount.text = entity.rewardAmount
        }
    }

    private fun getItemData(): RankEntity? = data?.castObject<RankEntity>()
}