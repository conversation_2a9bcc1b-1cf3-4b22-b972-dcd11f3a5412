package com.fascin.chatter.main.task

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.exts.getColorEx
import kotlinx.android.synthetic.main.activity_task_historical_list.rvHistoricalList

/**
 * @Desc: 历史任务列表
 * @Created: QuanZH
 * @Date: 2023/9/6
 */
class TaskHistoricalListActivity : ChatterBaseActivity(), IRvItemAction {

    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var viewModel: TaskViewModel? = null
    private var refreshing: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_task_historical_list)
        setTitle(R.string.task_historical_list_title)
        titleTv.setTextColor(getColorEx(R.color.black))
        initRv()
        setPreView()
        setViewModel()
        fetchPageData()
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(supportFragmentManager, rvHistoricalList, null).also {
            it.setCanPullDown(false)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                }

                override fun onFooterRefresh() {
                }
            })
        }
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(TaskViewModel::class.java).apply {
            taskHistoricalListLiveData.observe(this@TaskHistoricalListActivity) {
                refreshing = false
                recyclerViewSupport?.onHeaderRefreshComplete()
                setRvData(it)
            }
            taskHistoricalListErrorLiveData.observe(this@TaskHistoricalListActivity) {
                refreshing = false
                recyclerViewSupport?.onHeaderRefreshComplete()
                addErrorView()
            }
        }
    }

    private fun fetchPageData() {
        if (refreshing) return
        refreshing = true
        viewModel?.getTaskHistoricalList()
    }

    private fun setRvData(entityList: List<TaskWeekTitleEntity>?) {
        if (entityList.isNullOrEmpty()) {
            addEmptyView()
            return
        }
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(entityList, AppViewType.taskHistoricalListItemView, this))
        })
    }

    private fun setPreView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.taskHistoricalPlaceItemView, this))
        })
    }


    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) recyclerViewSupport?.updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.title = getString(R.string.page_data_tips)
            it.icRes = R.mipmap.ic_msg_setting_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            })
        }
    }

    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    override fun onRefresh() {
        fetchPageData()
    }
}