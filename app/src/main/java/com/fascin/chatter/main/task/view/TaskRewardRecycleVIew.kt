package com.fascin.chatter.main.task.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R

/**
 * @Desc: 用于解决任务卡片内，多天数完成状态横向滑动冲突
 * @Created: Quan
 * @Date: 2024/7/24
 */
class TaskRewardRecycleVIew @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    override fun onInterceptTouchEvent(e: MotionEvent): Bo<PERSON>an {
        if (isTouchOnScrollView(e)) {
            return false
        }
        return super.onInterceptTouchEvent(e)
    }

    private fun isTouchOnScrollView(e: MotionEvent): Boolean {
        val childView = findChildViewUnder(e.x, e.y)
        if (childView is ViewGroup) {
            for (i in 0 until childView.childCount) {
                val child = childView.getChildAt(i)
                if (child is ViewGroup) {
                    for (j in 0 until child.childCount) {
                        val child2 = child.getChildAt(j)
                        // 找到day view进度条布局，判断点击位置是否是进度条布局内，是的话拦截
                        if (child2 != null && child2.id == R.id.llProgressInfo && child2 is ViewGroup) {
                            return true
                        }
                    }
                }
            }
        }
        return false
    }
}