package com.fascin.chatter.main.chats

import com.fascin.chatter.bean.AiAutoChatEntity
import com.fascin.chatter.bean.event.UIAiChatChangeEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import io.rong.imkit.notification.RongNotificationManager
import java.util.Collections

/**
 * @Desc:ai会话的相关操作
 * @Created: Quan
 * @Date: 2024/8/19
 */
object AiAutoChatHelper {

    private var aiImIdList = Collections.synchronizedList(ArrayList<String>())

    /**
     * 重置数据
     */
    fun resetAiImIdList(data: ArrayList<AiAutoChatEntity>) {
        aiImIdList.clear()
        if (data.isNotEmpty()) {
            aiImIdList.addAll(data.map { it.imid })
        }
    }


    /**
     * 是否是 ai会话
     */
    fun isAiImId(imId: String): Boolean {
        return aiImIdList.contains(imId.orEmpty())
    }

    /**
     * imId状态改变,更新数据和UI
     * @param status 1 表示变成 ai会话 ，需要隐藏该会话数据；0 表示关闭 ai ，需要在页面显示该会话数据
     */
    fun aiChatChange(imId: String, status: Int) {
        if (status == 0 && isAiImId(imId)) {
            aiImIdList.remove(imId)
            SimpleRxBus.post(UIAiChatChangeEvent())
        } else if (status == 1 && !isAiImId(imId)) {
            aiImIdList.add(imId)
            SimpleRxBus.post(UIAiChatChangeEvent())
        }
        RongNotificationManager.getInstance().setAiImIdList(aiImIdList)
    }
}