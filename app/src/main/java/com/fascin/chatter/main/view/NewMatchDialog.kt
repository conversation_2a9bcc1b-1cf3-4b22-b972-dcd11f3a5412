package com.fascin.chatter.main.view

import android.os.Build
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.component.player.SUHttpCacheServer
import com.fascin.chatter.component.player.SUMediaPlayer
import com.fascin.chatter.component.player.SURenderCallback
import com.fascin.chatter.component.player.SUVolumeEvent
import com.fascin.chatter.component.views.OnMediaChangedCallback
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.beans.EventNewMatchUpdate
import com.iandroid.allclass.lib_common.beans.GreetItem
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.disableCopy
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import io.rong.imkit.utils.keyboard.KeyboardHeightObserver
import io.rong.imkit.utils.keyboard.KeyboardHeightProvider
import kotlinx.android.synthetic.main.dialog_new_match.editInput
import kotlinx.android.synthetic.main.dialog_new_match.greetingChatList
import kotlinx.android.synthetic.main.dialog_new_match.ivClear
import kotlinx.android.synthetic.main.dialog_new_match.ivVip
import kotlinx.android.synthetic.main.dialog_new_match.llEdit
import kotlinx.android.synthetic.main.dialog_new_match.msgContinue
import kotlinx.android.synthetic.main.dialog_new_match.nickName
import kotlinx.android.synthetic.main.dialog_new_match.scrollView
import kotlinx.android.synthetic.main.dialog_new_match.userAge
import kotlinx.android.synthetic.main.dialog_new_match.userInterests
import kotlinx.android.synthetic.main.dialog_new_match.userMedias
import kotlinx.android.synthetic.main.dialog_new_match.userNearby
import kotlinx.android.synthetic.main.dialog_new_match.userOnline
import kotlinx.android.synthetic.main.dialog_new_match.userSign
import kotlinx.android.synthetic.main.item_medias_image.view.medias_image_view
import kotlinx.android.synthetic.main.item_medias_image.view.medias_video_container

/**
 * @Desc: new Match弹窗
 * @Created: Quan
 * @Date: 2023/12/4
 */
class NewMatchDialog() : BaseDialogFragment(), OnMediaChangedCallback {

    private var imId: String = ""
    private var viewModel: ProfileViewModel? = null
    private var player: SUMediaPlayer? = null
    private var selectGreetId: Int = 0
    private var selectGreetContent: String? = ""
    private var keyboardHeightProvider: KeyboardHeightProvider? = null

    constructor(imId: String) : this() {
        this.imId = imId
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_new_match, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.886).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false
        setEditView()
        initPlayer()
        setListener()
        requestData()
    }

    private fun initPlayer() {
        player = SUMediaPlayer(context!!)
    }

    private fun setEditView() {
        // 禁用edittext复制粘贴
        editInput.disableCopy()
        setEditUI()
        setContinueStatus()
        // 监听edit内容变化
        editInput.addTextChangedListener {
            setEditUI()
            setContinueStatus()
        }

        editInput.setOnFocusChangeListener { _, hasFocus ->
            llEdit.setBackgroundResource(
                if (hasFocus) R.drawable.bg_stroke_yellow_r12
                else R.drawable.bg_round_gray_radius_12
            )
        }
    }

    private fun setListener() {
        ivClear.clickWithTrigger {
            editInput.setText("")
        }

        msgContinue.clickWithTrigger {
            val msg = editInput.text?.trim().toString()
            KeyboardUtils.hideKeyboard(editInput)
            if (msg.isNotEmpty()) {
                msgContinue.setButtonStatus(SUButtonStatus.Loading)
                // 发送消息
                viewModel?.matchDialogSayHi(
                    imId, if (msg == selectGreetContent) selectGreetId.toString()
                    else "0", msg
                )
            }
        }

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(SUVolumeEvent::class) {
            player?.setVolume(it.volume)
            userMedias.showMuteView()
        })
    }

    private fun requestData() {
        viewModel = ViewModelProvider(this).get(ProfileViewModel::class.java)
        if (imId.isNotEmpty()) {
            viewModel?.getUserInfo(imId, 1)
            viewModel?.requestSayHiMsg(imId)
        }
        viewModel?.userInfoResult?.observe(this) {
            setUserInfo(it)
        }

        viewModel?.msgSayListHiResult?.observe(this) {
            setGreetList(it)
        }

        viewModel?.sayHiResult?.observe(this) {
            // sayHi成功，刷新sayHi列表
            SimpleRxBus.post(EventNewMatchUpdate())
            dismissAllowingStateLoss()
        }
    }

    private fun setEditUI() {
        ivClear.show(editInput.text?.trim().toString().isNotEmpty())
    }

    /**
     * 设置按钮状态
     */
    private fun setContinueStatus() {
        if (msgContinue.getButtonStatus() != SUButtonStatus.Loading) {
            if (editInput.text?.trim().toString().isNotEmpty()) {
                msgContinue.setButtonStatus(
                    SUButtonStatus.Activated
                )
                msgContinue.setText(getString(com.iandroid.allclass.lib_basecore.R.string.send))
            } else {
                msgContinue.setButtonStatus(
                    SUButtonStatus.Disabled, R.drawable.bg_d9d9d9_r16, "#bfbfbf"
                )
                msgContinue.setText(getString(R.string.new_match_empty_btn))
            }
        }
    }

    private fun setUserInfo(userInfo: UserEntity?) {
        userInfo?.let {
            userMedias?.apply {
                enableUserInputEvent(true)
                setImageRoundedCorner(0)
                setDataSource(it.mediaList.orEmpty(), true)
                showCornerMaskView(false)
                setOnMediaChangedCallback(this@NewMatchDialog)
            }
            nickName.text = it.nickname
            ivVip.show(it.vip == 1)
            userAge.text = buildString {
                append(",")
                append(it.age)
            }
            userOnline.show(UserOnlineHelper.isOnline(it.userId))
            userNearby.show(it.address.isNotEmpty())
            userNearby.text = it.address.ifEmpty { "" }
            userSign.text = it.sign
            userSign.show(it.sign.isNotEmpty())
            userInterests?.show(it.tags?.isNotEmpty() == true)
            userInterests?.apply {
                it.tags?.let { data ->
                    setDataSource(
                        data,
                        R.drawable.bg_tag_nor_white_30,
                        com.iandroid.allclass.lib_common.R.color.ffffff_80,
                        textSize = 13f,
                        verticalPadding = 4.toPx
                    )
                }
            }
        }
    }

    private fun setGreetList(list: ArrayList<GreetItem>) {
        list.shuffle()
        greetingChatList.updateView(list)
        greetingChatList.setClickCallback { id, greet ->
            selectGreet(id, greet)
        }
    }

    private fun selectGreet(id: Int, content: String) {
        selectGreetId = id
        selectGreetContent = content
        editInput.setText(content)
    }

    override fun onResume() {
        super.onResume()
        player?.start()
        editInput?.postDelayed({
            if (useKeyboardHeightProvider() && activity?.isDestroyed != true) {
                keyboardHeightProvider = KeyboardHeightProvider(activity)
                keyboardHeightProvider?.setKeyboardHeightObserver(mKeyboardHeightObserver)
            }
            keyboardHeightProvider?.start()
        }, 150)
    }

    override fun onPause() {
        super.onPause()
        player?.pause()
        if (keyboardHeightProvider != null) {
            keyboardHeightProvider!!.stop()
            keyboardHeightProvider!!.setKeyboardHeightObserver(null)
            keyboardHeightProvider = null
        }
    }

    override fun onDestroy() {
        KeyboardUtils.hideKeyboard(editInput)
        super.onDestroy()
    }

    private fun useKeyboardHeightProvider(): Boolean {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return activity?.isInMultiWindowMode == false
        }
        return false
    }

    private val mKeyboardHeightObserver =
        KeyboardHeightObserver { orientation, isOpen, keyboardHeight ->
            scrollView?.also {
                it.smoothScrollTo(0, it.height)
            }
        }

    override fun onMediaChanged(mediaEntity: MediaEntity) {
        val mediasView = userMedias.getCurrentView() ?: return
        if (mediaEntity.type == 1) {
            mediasView.medias_image_view.show(false)
            player?.attach(mediasView.medias_video_container, object : SURenderCallback {
                override fun onRenderStart() {
                    mediasView.medias_image_view.show(false)
                    mediasView.medias_video_container.alpha = 1F
                }
            })
            player?.setDataSource(SUHttpCacheServer.getProxyUrl(mediaEntity.url))
            mediasView.post { mediasView.medias_image_view.show(true) }
            userMedias.showMuteView()
        } else {
            player?.detach()
            userMedias.hideMuteView()
        }
    }

    override fun onMuteTapped() {
    }

    companion object {
        fun showNotifyItem(imId: String) {
            if (imId.isNotEmpty()) {
                AppContext.getTopFragmentActivity()?.also {
                    NewMatchDialog(imId).show(
                        it.supportFragmentManager, NewMatchDialog::javaClass.name
                    )
                }
            }
        }
    }
}