package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.event.UIToMPCEvent
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_repossession_remind.ivClose
import kotlinx.android.synthetic.main.dialog_repossession_remind.tvGot

/**
 * 主播账号回收预警弹窗
 */
class RepossessionDialog() : BaseDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_repossession_remind, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.86).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false

        ivClose.clickWithTrigger {
            dismissAllowingStateLoss()
        }

        tvGot.clickWithTrigger {
            // 跳转MPC页面
            SimpleRxBus.post(UIToMPCEvent())
            dismissAllowingStateLoss()
        }
    }
}