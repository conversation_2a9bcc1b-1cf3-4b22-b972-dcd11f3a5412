package com.fascin.chatter.main.task

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.DailyRankEntity
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.fragment_rank.rvRank

class RankFragment : BaseUiFragment(), IRvItemAction {

    private var viewModel: TaskViewModel? = null
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var refreshing: Boolean = false
    private var type: Int = 0 // 1: newbie 0: W2
    private var rankData: DailyRankEntity? = null

    override fun attachLayoutId(): Int {
        return R.layout.fragment_rank
    }

    override fun initView(view: View?) {
        super.initView(view)
        initRv()
        // 设置骨骼视图
        setPreView()
        setViewModel()
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, rvRank, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    if (!refreshing) fetchPageData()
                    else recyclerViewSupport?.onHeaderRefreshComplete()
                }

                override fun onFooterRefresh() {

                }
            })
        }
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(TaskViewModel::class.java).apply {
            rankDataLiveData.observe(this@RankFragment) {
                refreshing = false
                recyclerViewSupport?.onHeaderRefreshComplete()
                rankData = it
                setRvData()
                setActivityTopData()
            }

            rankDataError.observe(this@RankFragment) {
                refreshing = false
                if (rankData?.rankList.isNullOrEmpty()) {
                    addErrorView()
                }
            }
        }
    }

    private fun setPreView() {

    }

    /**
     * 获取数据
     */
    fun fetchPageData() {
        refreshing = true
        // 是否w1主播：0 不是 1 是
        viewModel?.getRankData(type)
    }

    private fun setRvData() {
        updateData(ArrayList<BaseRvItemInfo>().also { list ->
            if (rankData?.rankList?.isNotEmpty() == true) {
                rankData?.rankList?.forEach { crank ->
                    crank.showReward = rankData?.showReward ?: 0
                }
                list.add(
                    BaseRvItemInfo(
                        rankData?.also {
                            it.type = type
                        },
                        AppViewType.rankHeadItemView,
                        this
                    )
                )
                rankData?.rankList?.forEachIndexed { index, rank ->
                    if (index >= 3) {
                        list.add(
                            BaseRvItemInfo(
                                rank,
                                AppViewType.rankItemView,
                                this
                            )
                        )
                    }
                }
            }
        })
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo>?) {
        if (itemTemp?.isNotEmpty() == true) recyclerViewSupport?.updateData(itemTemp, true)
        else addEmptyView()
    }


    /**
     * 数据错误视图
     */
    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    /**
     * 空视图
     */
    private fun addEmptyView() {
        updateData(ArrayList<BaseRvItemInfo>().also { list ->
            list.add(BaseRvItemInfo(EmptyEntity().also {
                it.content = getString(R.string.page_data_tips)
                it.icRes = R.mipmap.ic_public_album_empty
            }, AppViewType.comEmptyView, this))
        })
    }

    fun setFragmentTab(type: Int) {
        this.type = type
        fetchPageData()
    }

    /**
     * 设置页面顶部时间和右上角数据
     */
    private fun setActivityTopData() {
        rankData?.let {
            context.castObject<DailyRankActivity>()?.let { rankActivity ->
                rankActivity.setRuleTime(it.rule, it.expireTime)
            }
        }
    }

    override fun onRefresh() {
        fetchPageData()
    }
}