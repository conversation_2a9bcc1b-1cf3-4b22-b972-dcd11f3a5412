package com.fascin.chatter.main.task.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.RewardCardChildEntity
import com.fascin.chatter.bean.TaskRewardCardEntity
import com.fascin.chatter.main.task.view.WeekTaskProgressView
import com.iandroid.allclass.lib_common.utils.exts.setViewMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.cardView
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.llContent
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.llProgressInfo
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.placeholder
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.tvRewardDesc
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.tvStatus
import kotlinx.android.synthetic.main.itemview_task_reward_card.view.tvTitle

/**
 * @Desc: 任务说明弹窗表格adapter
 * @Created: Quan
 * @Date: 2023/11/10
 */
class TaskRewardAdapter(var context: Context) : RecyclerView.Adapter<TaskRewardAdapter.ViewHolder>() {

    private val dataList = mutableListOf<TaskRewardCardEntity>()
    private var maxLengthText: String = "" // 卡片列表item中最长标题的name，用于计算卡片列表item的高度

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<TaskRewardCardEntity>?) {
        dataList.clear()
        maxLengthText = ""
        if (data?.isNotEmpty() == true) {
            data.maxByOrNull { it.name.length }?.let {
                maxLengthText = it.name
            }
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    fun getItem(position: Int): TaskRewardCardEntity {
        return dataList[position]
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.itemview_task_reward_card, parent, false)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        setBgColor(position, holder)
        holder.itemView.let { itemView ->
            if (position == itemCount - 1 && position > 0)
                itemView.cardView.setViewMargin(right = 16f.toPx.toInt())
            getItem(position).let {
                itemView.placeholder.text = maxLengthText.ifEmpty { it.name }
                itemView.tvTitle.text = it.name
                itemView.tvRewardDesc.text = buildString {
                    append("Reward: ")
                    append(it.reward)
                }
                itemView.tvStatus.show(it.achieved == 1, true)
                addProgressView(itemView.llProgressInfo, it.list!!)
            }
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    private fun setBgColor(position: Int, holder: ViewHolder) {
        holder.itemView?.let {
            when (position % 4) {
                0 -> {
                    it.llContent.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_c3b1e1)
                    it.tvRewardDesc.textColorResource = com.iandroid.allclass.lib_common.R.color.color_c3b1e1
                }

                1 -> {
                    it.llContent.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_e9967a)
                    it.tvRewardDesc.textColorResource = com.iandroid.allclass.lib_common.R.color.color_e9967a
                }

                2 -> {
                    it.llContent.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_c1d7c1)
                    it.tvRewardDesc.textColorResource = com.iandroid.allclass.lib_common.R.color.color_c1d7c1
                }

                3 -> {
                    it.llContent.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_a7bed3)
                    it.tvRewardDesc.textColorResource = com.iandroid.allclass.lib_common.R.color.color_a7bed3
                }
            }
        }
    }

    private fun addProgressView(progressLayout: LinearLayout?, cardChildList: List<RewardCardChildEntity>) {
        progressLayout?.removeAllViews()
        cardChildList?.sortedByDescending { it.must }?.forEachIndexed { index, child ->
            progressLayout?.addView(
                WeekTaskProgressView(context).apply {
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT
                    ).apply {
                        if (index != cardChildList.size - 1)
                            bottomMargin = 16f.toPx.toInt()
                    }
                    setViewData(child)
                },
                index
            )
        }
    }
}
