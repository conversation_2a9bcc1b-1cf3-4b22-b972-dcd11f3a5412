package com.fascin.chatter.main.chats

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.chat.UserImExtraEntity
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.utils.GsonUtils
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.userinfo.RongUserInfoManager
import kotlinx.android.synthetic.main.dialog_chats_filter.FilteReset
import kotlinx.android.synthetic.main.dialog_chats_filter.FilteviewCountChats
import kotlinx.android.synthetic.main.dialog_chats_filter.LvlAll
import kotlinx.android.synthetic.main.dialog_chats_filter.LvlLvl1
import kotlinx.android.synthetic.main.dialog_chats_filter.LvlLvl2
import kotlinx.android.synthetic.main.dialog_chats_filter.LvlLvl3
import kotlinx.android.synthetic.main.dialog_chats_filter.SubFilterRGLine
import kotlinx.android.synthetic.main.dialog_chats_filter.SubFilterRGLvl
import kotlinx.android.synthetic.main.dialog_chats_filter.SubFilterRGMember
import kotlinx.android.synthetic.main.dialog_chats_filter.SubFilterRGNew
import kotlinx.android.synthetic.main.dialog_chats_filter.lineAll
import kotlinx.android.synthetic.main.dialog_chats_filter.lineOffline
import kotlinx.android.synthetic.main.dialog_chats_filter.lineOnline
import kotlinx.android.synthetic.main.dialog_chats_filter.memberAll
import kotlinx.android.synthetic.main.dialog_chats_filter.memberMember
import kotlinx.android.synthetic.main.dialog_chats_filter.memberNoMember
import kotlinx.android.synthetic.main.dialog_chats_filter.newAll
import kotlinx.android.synthetic.main.dialog_chats_filter.newNew
import kotlinx.android.synthetic.main.dialog_chats_filter.newNonNew

class ChatsFilterDialog(
    var memberFlag: Int,
    var activeFlag: Int,
    var gradleFlag: Int,
    var newFlag: Int,
    var chatList: List<BaseUiConversation>,
    private val completeBlock: (Int, Int, Int, Int, Boolean) -> Unit
) : BaseDialogFragment() {

    override fun onStart() {
        super.onStart()
        setTopPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_chats_filter, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        chatsFilterNum()
        //重置
        FilteReset.setOnClickListener {
            memberFlag = ChatsConfig.FlagMemberAll
            memberAll.isChecked = memberFlag == ChatsConfig.FlagMemberAll
            activeFlag = ChatsConfig.FlagActiveAll
            lineAll.isChecked = activeFlag == ChatsConfig.FlagActiveAll
            gradleFlag = ChatsConfig.FlagLvlAll
            LvlAll.isChecked = gradleFlag == ChatsConfig.FlagLvlAll
            newFlag = ChatsConfig.FlagNewAll
            newAll.isChecked = newFlag == ChatsConfig.FlagNewAll
            FilteviewCountChats.text = "View ${chatList.size} Chats"
        }
        //确定筛选
        FilteviewCountChats.setOnClickListener {
            val isOpenFilter = memberFlag == ChatsConfig.FlagMemberAll
                    && activeFlag == ChatsConfig.FlagActiveAll
                    && gradleFlag == ChatsConfig.FlagLvlAll
                    && newFlag == ChatsConfig.FlagNewAll
            completeBlock.invoke(memberFlag, activeFlag, gradleFlag, newFlag, isOpenFilter)
            AppRepository.eventTrace(EventKey.K_allchats_fliter_viewchats)
            dismissAllowingStateLoss()
        }

        //member
        memberAll.isChecked = memberFlag == ChatsConfig.FlagMemberAll
        memberMember.isChecked = memberFlag == ChatsConfig.FlagMemberMember
        memberNoMember.isChecked = memberFlag == ChatsConfig.FlagMemberNoMember
        SubFilterRGMember.setOnCheckedChangeListener { radioGroup, id ->
            when (id) {
                R.id.memberAll -> {
                    memberFlag = ChatsConfig.FlagMemberAll
                }

                R.id.memberMember -> {
                    memberFlag = ChatsConfig.FlagMemberMember
                }

                R.id.memberNoMember -> {
                    memberFlag = ChatsConfig.FlagMemberNoMember
                }
            }
            chatsFilterNum()
        }

        //active
        lineAll.isChecked = activeFlag == ChatsConfig.FlagActiveAll
        lineOnline.isChecked = activeFlag == ChatsConfig.FlagActiveOnline
        lineOffline.isChecked = activeFlag == ChatsConfig.FlagActiveOffline

        SubFilterRGLine.setOnCheckedChangeListener { radioGroup, id ->
            when (id) {
                R.id.lineAll -> {
                    activeFlag = ChatsConfig.FlagActiveAll
                }

                R.id.lineOnline -> {
                    activeFlag = ChatsConfig.FlagActiveOnline
                }

                R.id.lineOffline -> {
                    activeFlag = ChatsConfig.FlagActiveOffline
                }
            }
            chatsFilterNum()
        }

        //gradle
        LvlAll.isChecked = gradleFlag == ChatsConfig.FlagLvlAll
        LvlLvl1.isChecked = gradleFlag == ChatsConfig.FlagLvlLv1
        LvlLvl2.isChecked = gradleFlag == ChatsConfig.FlagLvlLv2
        LvlLvl3.isChecked = gradleFlag == ChatsConfig.FlagLvlLv3

        SubFilterRGLvl.setOnCheckedChangeListener { radioGroup, id ->
            when (id) {
                R.id.LvlAll -> {
                    gradleFlag = ChatsConfig.FlagLvlAll
                }

                R.id.LvlLvl1 -> {
                    gradleFlag = ChatsConfig.FlagLvlLv1
                }

                R.id.LvlLvl2 -> {
                    gradleFlag = ChatsConfig.FlagLvlLv2
                }

                R.id.LvlLvl3 -> {
                    gradleFlag = ChatsConfig.FlagLvlLv3
                }
            }
            chatsFilterNum()
        }

        //new
        newAll.isChecked = newFlag == ChatsConfig.FlagNewAll
        newNew.isChecked = newFlag == ChatsConfig.FlagNew
        newNonNew.isChecked = newFlag == ChatsConfig.FlagNonNew
        SubFilterRGNew.setOnCheckedChangeListener { radioGroup, id ->
            when (id) {
                R.id.newAll -> {
                    newFlag = ChatsConfig.FlagNewAll
                }

                R.id.newNew -> {
                    newFlag = ChatsConfig.FlagNew
                }

                R.id.newNonNew -> {
                    newFlag = ChatsConfig.FlagNonNew
                }
            }
            chatsFilterNum()
        }

    }

    /**
     * 过滤数据展示数量
     */
    private fun chatsFilterNum() {
        val filteredList = chatList.filter { chatItem ->
            val userInfo = RongUserInfoManager.getInstance().getUserInfo(chatItem.mCore.targetId)
            val userImExtra = userInfo?.extra?.let {
                GsonUtils.fromJson(it, UserImExtraEntity::class.java)
            }
            val hasUnlock = if (userImExtra != null && userImExtra.unlockNum > 0) 1 else 0
            //过期vip按 非vip 处理 (memberFlag == 0 && userImExtra?.vip == 2)
            val memberFlagCondition =
                memberFlag == -1 || userImExtra?.vip == memberFlag || (memberFlag == 0 && userImExtra?.vip == 2)
            val activeFlagCondition =
                activeFlag == -1 || UserOnlineHelper.getOnline(chatItem.mCore.targetId) == activeFlag
            val chatLevelCondition = gradleFlag == -1 || userImExtra?.chatLevel == gradleFlag
            val newFlagCondition = newFlag == -1 || userImExtra?.newIceBeak == newFlag
            chatLevelCondition && memberFlagCondition && activeFlagCondition && newFlagCondition
        }
        FilteviewCountChats.text = "View ${filteredList.size} Chats"
    }

    companion object {
    }
}