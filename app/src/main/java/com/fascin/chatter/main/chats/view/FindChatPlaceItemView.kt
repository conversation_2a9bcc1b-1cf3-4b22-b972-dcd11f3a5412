package com.fascin.chatter.main.chats.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType

/**
 * FindChat Item
 * @Created: QuanZH
 * @Date: 2023/9/20
 */
@RvItem(id = AppViewType.findChatsPlaceItemView, spanCount = 1)
class FindChatPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_find_chat_place
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {

    }

}