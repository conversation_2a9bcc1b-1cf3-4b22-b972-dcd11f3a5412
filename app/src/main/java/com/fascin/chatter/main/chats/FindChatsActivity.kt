package com.fascin.chatter.main.chats

import android.os.Bundle
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.FindChatIntent
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.event.UIFindChatInitDataEvent
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.main.adapter.ChattersModelListAdapter
import com.fascin.chatter.main.adapter.TabAdapter
import com.fascin.chatter.repository.AppRepository
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.beans.HomeTabEntity
import com.iandroid.allclass.lib_common.beans.MixPageEntity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.event.RefreshOnlineUIEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.objToJsonString
import kotlinx.android.synthetic.main.activity_find_chats.findChatVP
import kotlinx.android.synthetic.main.activity_find_chats.rvModel
import kotlinx.android.synthetic.main.activity_find_chats.tabLayout

/**
 * @Desc:
 * @Created: Quan
 * @Date: 2023/12/13
 */
class FindChatsActivity : ChatterBaseActivity(), TabAdapter.IFragmentTabCreator {

    private var tabAdapter: TabAdapter? = null
    private var modelAdapter: ChattersModelListAdapter? = null
    var modelList: ArrayList<ModelUserEntity>? = ArrayList()
    private var viewModel: ChatsViewModel? = null
    private var chats: HashMap<String, ArrayList<ExploreUserEntity>>? = HashMap()
    private var firstRequest: Boolean = true

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_find_chats)
        AppModule.userActive()
        showTitleBar(true)
        setTitle(R.string.find_chats_title)
        initModelRV()
        initTabAndViewPager()
        setTabViewPagerCallback()
        setViewModel()
        AppRepository.eventTrace(EventKey.K_findvip_page_view)
    }

    private fun initModelRV() {
        parseJsonParams<FindChatIntent>()?.let {
            modelList = it.list
        }
        rvModel.layoutManager =
            LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        modelAdapter = ChattersModelListAdapter { userId, reClick ->
            // 当前不是match页时，并以是该model时，跳model主页
            if (!reClick) {
                // 切换tab,筛选数据
                if (chats?.containsKey(userId) == false) {
                    tabAdapter?.fragmentHashList?.forEach {
                        it.value.castObject<FindChatsFragment>()?.also { fragment ->
                            fragment.setPreView()
                        }
                    }
                }
                setFragmentData()
            }
        }
        rvModel.adapter = modelAdapter
        modelAdapter?.isShowUnreadNum(false)
        modelAdapter?.updateData(modelList!!)
    }

    private fun initTabAndViewPager() {
        findChatVP.offscreenPageLimit = 3
        val tabList = TabConfig.getFindChatsTabList()
        tabAdapter = TabAdapter(tabList, supportFragmentManager, lifecycle)
        val autoShowIndex = getDefaultShowTabIndex(tabList)
        findChatVP.adapter = tabAdapter.also { it?.iTabFragmentCreator = this }
        TabLayoutMediator(tabLayout, findChatVP) { tab, position ->
            tab.text = tabList[position].title
        }.attach()

        for (index in tabList.indices) {
            val tab: TabLayout.Tab? = tabLayout.getTabAt(index)
            tab?.setCustomView(R.layout.layout_find_chat_tab_item)
            tab?.customView?.run {
                val tabItemViewTitle = findViewById<TextView>(R.id.tv_tab_name)
                tabItemViewTitle.text = tabList[index].title
                if (index == autoShowIndex) {
                    tabItemViewTitle.isSelected = true
                }
            }
        }

        tabLayout.postDelayed({
            findChatVP?.setCurrentItem(autoShowIndex, false)
        }, 50)
    }

    private fun setTabViewPagerCallback() {
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    val tabTitle = findViewById<TextView>(R.id.tv_tab_name)
                    tabTitle.isSelected = true
                    tabAdapter.castObject<TabAdapter>()?.getFragment(tab.position)
                        ?.castObject<FindChatsFragment>()?.also {
                            it.fragmentVisible(true)
                        }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    val tabTitle = findViewById<TextView>(R.id.tv_tab_name)
                    tabTitle.isSelected = false
                    tabAdapter.castObject<TabAdapter>()?.getFragment(tab.position)
                        ?.castObject<FindChatsFragment>()?.also {
                            it.fragmentVisible(false)
                        }
                }
            }

            override fun onTabReselected(p0: TabLayout.Tab?) {
            }
        })
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(ChatsViewModel::class.java)
        viewModel?.findChatsLiveData?.observe(this) {
            refreshComplete()
            chats?.put(it.modelId, it.list!!)
//            setModelOnlineNum()
            if (modelAdapter?.curSelUserId == it.modelId)
                setFragmentData()
        }
        viewModel?.findChatsError?.observe(this) {
            refreshComplete()
            if (modelAdapter?.curSelUserId == it)
                setFragmentData(true)
        }
        if (modelList?.isNotEmpty() == true)
            getFindChats(modelList!![0].userId.toInt())
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RefreshOnlineUIEvent::class) {
            // 刷新列表
            tabAdapter?.fragmentHashList?.forEach {
                it.value.castObject<FindChatsFragment>()?.also { fragment ->
                    fragment.updatePageData(chats?.get(modelAdapter?.curSelUserId)!!)
//                    setModelOnlineNum()
                }
            }
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIFindChatInitDataEvent::class) {
            getFindChats(0, true)
        })
    }

    private fun getFindChats(modelId: Int, getCurrent: Boolean = false) {
        viewModel?.getFindModelChatsList(
            if (getCurrent)
                modelAdapter?.curSelUserId?.toInt()!!
            else
                modelId
        )
    }

    private fun refreshComplete() {
        tabAdapter?.fragmentHashList?.forEach {
            it.value.castObject<FindChatsFragment>()?.also { fragment ->
                fragment.onRefreshComplete()
            }
        }
    }

    private fun setFragmentData(isError: Boolean = false) {
        if (isError) {
            tabAdapter?.fragmentHashList?.forEach {
                it.value.castObject<FindChatsFragment>()?.also { fragment ->
                    fragment.updatePageData(null, true)
                }
            }
        } else if (chats?.containsKey(modelAdapter?.curSelUserId) == true) {
            tabAdapter?.fragmentHashList?.forEach {
                it.value.castObject<FindChatsFragment>()?.also { fragment ->
                    fragment.updatePageData(chats?.get(modelAdapter?.curSelUserId)!!)
                }
            }
        } else {
            getFindChats(modelAdapter?.curSelUserId?.toInt()!!)
        }
    }

    /**
     * 默认先展示的tab
     */
    private fun getDefaultShowTabIndex(homeTabList: List<HomeTabEntity>): Int {
        for (index in homeTabList.indices) {
            if (homeTabList[index].default > 0) return index
        }
        return 0
    }

    override fun onResume() {
        super.onResume()
        if (modelAdapter?.curSelUserId != null && !firstRequest) {
            firstRequest = false
            getFindChats(modelAdapter?.curSelUserId?.toInt()!!)
        }
    }

    override fun onCreateTabFragment(tab: HomeTabEntity): Fragment {
        return FindChatsFragment().apply {
            arguments = Bundle().also {
                it.putString(
                    Values.intentJsonParam,
                    MixPageEntity(
                        tab.title,
                        tab.url,
                        tab.id
                    ).objToJsonString<MixPageEntity>()
                )
            }
        }
    }

    private fun getModelId(imUserId: String): String {
        var targetIdIndex = imUserId.indexOf("_")
        if (targetIdIndex > 0) {
            return imUserId.substring(0, targetIdIndex)
        }
        return ""
    }

}