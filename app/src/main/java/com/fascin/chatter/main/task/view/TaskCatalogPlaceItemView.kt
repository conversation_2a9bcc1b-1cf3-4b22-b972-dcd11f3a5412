package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType

/**
 * @Desc: task模块:Task目录item预加载
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
@RvItem(id = AppViewType.taskCatalogPlaceItemView, spanCount = 1)
class TaskCatalogPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {
    override fun attachLayoutId(): Int {
        return R.layout.itemview_catalog_task_plece
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun setView() {
    }
}