package com.fascin.chatter.main.chats

import android.os.Bundle
import android.view.MotionEvent
import android.widget.EditText
import androidx.core.widget.addTextChangedListener
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.im.UserOnlineHelper
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import kotlinx.android.synthetic.main.activity_vip_contact_search.btnCancel
import kotlinx.android.synthetic.main.activity_vip_contact_search.etSearch
import kotlinx.android.synthetic.main.activity_vip_contact_search.immersiveId
import kotlinx.android.synthetic.main.activity_vip_contact_search.ivClear
import kotlinx.android.synthetic.main.activity_vip_contact_search.rvSearch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * @Desc:首页VIP模块搜索功能
 * @Created: Quan
 * @Date: 2024/7/31
 */
class VipContactSearchActivity : FasBaseActivity() {

    private var chats: ArrayList<ExploreUserEntity> = ArrayList()
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var job: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_vip_contact_search)
        immersiveStatusBarWithMargin(immersiveId)
        initViewData()
        initSearchView()
    }

    private fun initViewData() {
        recyclerViewSupport = RecyclerViewSupport(supportFragmentManager, rvSearch, null).also {
            it.setCanPullDown(false)
            it.setCanPullUp(false)
        }
        ChatsConfig.shareViewModel?.vipConflateLiveData?.value?.let {
            it.let { datas ->
                chats = datas
            }
        }

        postDelayed({
            KeyboardUtils.showKeyboard(etSearch)
        }, 200L)

    }

    private fun initSearchView() {
        etSearch.addTextChangedListener {
            ivClear.show(it?.isNotEmpty() == true)
            // 任务未完成时，取消任务
            job?.cancel()
            // 开始任务
            job = CoroutineScope(Dispatchers.Main).launch {
                // 内容在600ms内不在变化时，执行搜索
                delay(600)
                search(etSearch.text.toString().trim())
            }
        }

        ivClear.clickWithTrigger {
            etSearch.setText("")
        }

        btnCancel.clickWithTrigger {
            finish()
        }
    }

    private fun search(searchStr: String) {
        if (searchStr.isNotEmpty()) {
            // 不区分大小写
            chats.filter { it.nickname.uppercase().contains(searchStr.uppercase()) }.let {
                val filterUser = if (it.isNotEmpty()) {
                    it as ArrayList<ExploreUserEntity>
                } else {
                    ArrayList<ExploreUserEntity>()
                }
                sortData(filterUser)
                setSearchData(filterUser)
            }
        }else{
            updateData(ArrayList<BaseRvItemInfo?>(), true)
        }
    }


    private fun setSearchData(searchData: List<ExploreUserEntity>?) {
        setData(
            ArrayList<BaseRvItemInfo?>().also {
                searchData?.forEach { entity ->
                    it.add(BaseRvItemInfo(entity, AppViewType.findChatsItemView, this))
                }
            }, true
        )
    }

    private fun setData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>?, clearData: Boolean) {
        if (recyclerViewSupport != null) recyclerViewSupport?.updateData(itemTemp, clearData)
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.title = getString(R.string.page_nodata_search)
            it.content = getString(R.string.page_nodata_search_desc)
            it.icRes = R.drawable.ic_vip_search_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            }, true)
        }
    }

    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        }, true)
    }

    /**
     * 排序
     */
    private fun sortData(chatList: ArrayList<ExploreUserEntity>?) {
        chatList?.sortWith(compareByDescending<ExploreUserEntity> {
            // 按model解锁数排
            it.unlockNum
        }.thenByDescending {
            // 先按在线排
            UserOnlineHelper.getOnline(it.imId)
        }.thenByDescending {
            // 再按活跃状态排
            UserOnlineHelper.isActive(it.imId, it.lastActiveTime)
        })
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (ev?.action == MotionEvent.ACTION_DOWN) {
            val v = currentFocus
            if (v != null && v is EditText) {
                val outRect = android.graphics.Rect()
                v.getGlobalVisibleRect(outRect)
                if (!outRect.contains(ev.rawX.toInt(), ev.rawY.toInt())) {
                    v.clearFocus()
                    KeyboardUtils.hideKeyboard(v)
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}