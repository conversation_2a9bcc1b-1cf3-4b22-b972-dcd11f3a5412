package com.fascin.chatter.main.chats

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.AiAutoChatEntity
import com.fascin.chatter.bean.BonusMatchHubEntity
import com.fascin.chatter.bean.BonusTipEntity
import com.fascin.chatter.bean.FindChatEntity
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.RevitalizeInEntity
import com.fascin.chatter.bean.ShiftEntranceEntity
import com.fascin.chatter.bean.WarningCheckEntity
import com.fascin.chatter.bean.chat.SaveChatEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.utils.SPUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.litepal.LitePal
import java.util.concurrent.ConcurrentHashMap


/**
created by wangkm
on 2020/9/12.
 */
class ChatsViewModel : BaseViewModel() {
    val chatterModelLiveData = MutableLiveData<ArrayList<ModelUserEntity>>()
    val aiAutoChatLiveData = MutableLiveData<ArrayList<AiAutoChatEntity>>()
    val aiAutoChatError = MutableLiveData<String>()
    val findChatsLiveData = MutableLiveData<FindChatEntity>()
    val findChatsError = MutableLiveData<String>()
    val vipConflateLiveData = MutableLiveData<ArrayList<ExploreUserEntity>>()
    val vipConflateError = MutableLiveData<String>()
    val findLostLiveData = MutableLiveData<FindChatEntity>()
    val findLostError = MutableLiveData<String>()
    val saveChatsLiveData = MutableLiveData<HashSet<String>>()
    val chatterErrorMsg = MutableLiveData<String>()
    val revitalizeInResult = MutableLiveData<RevitalizeInEntity>()
    val revitalizeInError = MutableLiveData<String>()
    val shiftsInResult = MutableLiveData<ShiftEntranceEntity>()
    val shiftsInError = MutableLiveData<String>()
    val readLiveData = MutableLiveData<Boolean>()

    val bonusTipResult = MutableLiveData<BonusTipEntity?>()
    val warningCheckResult = MutableLiveData<WarningCheckEntity>()
    val warningCheckError = MutableLiveData<String>()

    // 用于缓存的所有model的save chat
    private var allSaveChats = ConcurrentHashMap<String, HashSet<String>>()

    fun getChatterModelList() {
        compositeDisposable?.add(
            AppRepository.getChatterModelList()
                .subscribe({
                    chatterModelLiveData.postValue(it)
                }, {
                    chatterErrorMsg.postValue(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 获取ai自动聊天的会话id列表
     */
    fun getAiAutoChatId() {
        compositeDisposable?.add(
            AppRepository.getAutoChatIds()
                .subscribe({
                    aiAutoChatLiveData.postValue(it)
                }, {
                    aiAutoChatError.postValue(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 获取指定model的所有save chat targetId
     */
    suspend fun getTargetIdFromModelId(modelId: String) {
        withContext(Dispatchers.IO) {
            val startTime = System.currentTimeMillis()
            var exception: String = ""
            if (!allSaveChats.containsKey(modelId)) {
                try {
                    val saveChatEntities =
                        LitePal.where("modelId like ?", modelId)
                            .find(SaveChatEntity::class.java)
                    val saveChats = HashSet<String>()
                    saveChatEntities.forEach {
                        saveChats.add(it.targetId)
                    }
                    allSaveChats[modelId] = saveChats
                } catch (e: Exception) {
                    exception = e.javaClass.name + ":" + e.message
                }
            }
            traceErrorOrTimeout(startTime, exception, "getTargetIdFromModelId")
            saveChatsLiveData.postValue(allSaveChats[modelId])
        }
    }

    /**
     * 保存会话到本地数据库
     */
    fun saveConversationToDB(targetId: String) {
        if (allSaveChats.containsKey(getModelId(targetId))) {
            allSaveChats[getModelId(targetId)]?.add(targetId)
        }
        val saveChat = SaveChatEntity(
            targetId,
            getModelId(targetId)
        )
        saveChat.save()
    }

    /**
     * 取消保持
     */
    fun deleteConversationToDB(targetId: String) {
        if (allSaveChats.containsKey(getModelId(targetId))) {
            allSaveChats[getModelId(targetId)]?.remove(targetId)
        }
        LitePal.deleteAll(SaveChatEntity::class.java, "targetId like ?", targetId)
    }

    /**
     * 获取model id
     *
     * @param targetId targetId 格式为modelId_userId
     * @return model id
     */
    private fun getModelId(targetId: String): String {
        val targetIdIndex = targetId.indexOf("_")
        return if (targetIdIndex > 0) {
            targetId.substring(0, targetIdIndex)
        } else ""
    }

    /**
     * 更新会话隐身、在线、免打扰开关状态
     */
    fun updateChatFlag(imUid: String, chatFlag: Long, onComplete: (List<String>?) -> Unit) {
        compositeDisposable?.add(
            AppRepository.updateChatFlag(imUid, chatFlag)
                .subscribe({
                    onComplete(it.imIdList)
                }, {
                })
        )
    }

    /**
     * 获取FindChat列表数据
     */
    fun getFindModelChatsList(modelId: Int) {
        compositeDisposable?.add(
            AppRepository.getFindChatsTabList(modelId)
                .subscribe({
                    findChatsLiveData.postValue(FindChatEntity().also { entity ->
                        entity.modelId = modelId.toString()
                        entity.list = it.ifEmpty { ArrayList() }
                    })
                }, {
                    findChatsError.postValue(modelId.toString())
                })
        )
    }

    /**
     * 获取VIP合并展示的数据
     */
    fun getVIPConflateList() {
        compositeDisposable?.add(
            AppRepository.getVIPConflateList()
                .subscribe({
                    vipConflateLiveData.postValue(it)
                }, {
                    vipConflateError.postValue("")
                })
        )
    }

    /**
     * 更新联系人聊天tag
     */
    fun setContactTag(targetId: String, tagId: Int) {
        compositeDisposable?.add(
            AppRepository.updateContactTag(targetId, tagId)
                .subscribe({
                }, {
                })
        )
    }

    /**
     * 获取FindChat列表数据
     */
    fun getFindLostList(modelId: String) {
        compositeDisposable?.add(
            AppRepository.getFindLostList(modelId)
                .subscribe({
                    findLostLiveData.postValue(FindChatEntity().also { entity ->
                        entity.modelId = modelId
                        entity.list = it.ifEmpty { ArrayList() }
                    })
                    /*                    for (item in it) {
                                            val userInfo = UserInfo(item.imId, item.nickname, Uri.parse(item.avatarUrl))
                                            userInfo.extra = UserImExtraEntity().also { extra ->
                                                extra.chatLevel = AppController.getChatLvl(item.chatNum)
                                                extra.vip = item.vip
                                                extra.age = item.age
                                                extra.nearby = item.nearby
                                                extra.location = item.location
                                                extra.latitude = item.latitude
                                                extra.longitude = item.longitude
                                                extra.lastOnlineDay = item.lastOnlineDay
                                                extra.unlockNum = if (item.unlockNum > 0) 1 else 0
                                                extra.userChatNum = item.userChatNum
                                                extra.newIceBeak = item.newIceBeak
                                                extra.chatFlag = item.chatFlag
                                                extra.isNewUser = item.isNewUser
                                                extra.lastActiveTime = item.lastActiveTime
                                                extra.timeZoneID =
                                                    item.timeZoneID.ifEmpty { Values.chatDefaultTimeZone }
                                            }.toJsonString()
                                            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
                                        }*/
                }, {
                    findLostError.postValue(modelId)
                })
        )
    }

    /**
     * 老用户召回入口数据
     */
    fun showRevitalizeIn() {
        compositeDisposable?.add(
            AppRepository.getRevitalizeIn()
                .subscribe({
                    revitalizeInResult.postValue(it)
                }, {
                    revitalizeInError.postValue("")
                })
        )
    }

    /**
     * 排班入口数据
     */
    fun shiftEntranceData() {
        compositeDisposable?.add(
            AppRepository.shiftEntranceData()
                .subscribe({
                    shiftsInResult.postValue(it)
                }, {
                    shiftsInError.postValue("")
                })
        )
    }

    /**
     * 排班入口红点
     */
    fun readNewSchedule() {
        compositeDisposable?.add(
            AppRepository.readNewSchedule()
                .subscribe({
                    readLiveData.postValue(true)
                }, {
                    readLiveData.postValue(false)
                })
        )
    }

    /**
     * 首页BonusMatch提示条数据
     * @param click 是否点击触发的请求
     */
    fun getBonusTip(click: Boolean = false) {
        compositeDisposable?.add(
            AppRepository.getBonusTip()
                .subscribe({
                    bonusTipResult.postValue(it)
                }, {
                    bonusTipResult.postValue(null)
                })
        )
    }

    /**
     * 获取惩罚记录
     */
    fun warningCheckInfo(){
        compositeDisposable?.add(
            AppRepository.warningCheckInfo()
                .subscribe({
                    warningCheckResult.postValue(it)
                }, {
                    warningCheckError.postValue(it.message)
                })
        )
    }

    /**
     * 用于上报某个方法是否超时或者报错，只上报一次
     */
    fun traceErrorOrTimeout(startTime: Long = 0L, exception: String = "", funName: String) {
        val timeCount = System.currentTimeMillis() - startTime
        if (timeCount > 2000) {
            // 超过2s时上报，只上报一次，用来判断主播手机上的keep是否会出现这种情况
            if (SPUtils.getString(AppContext.context, "Time_$funName", "").isEmpty()) {
                SPUtils.put(AppContext.context, "Time_$funName", "1")
                AppRepository.eventTrace(EventKey.K_timeout_error) {
                    "fun name" to funName
                    "timeCount" to timeCount
                    "exception" to exception
                }
            }
        }
        if (exception.isNotEmpty()) {
            // 异常时上报，只上报一次，用来判断主播手机上的keep是否会出现这种情况
            if (SPUtils.getString(AppContext.context, "Exc_$funName", "").isEmpty()) {
                SPUtils.put(AppContext.context, "Exc_$funName", "1")
                AppRepository.eventTrace(EventKey.K_timeout_error) {
                    "funName" to funName
                    "timeCount" to timeCount
                    "exception" to exception
                }
            }
        }
    }


    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return ChatsViewModel() as T
        }
    }
}