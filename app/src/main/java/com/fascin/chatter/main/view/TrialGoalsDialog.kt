package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.GoalsEntity
import com.fascin.chatter.bean.event.UIToMPCEvent
import com.fascin.chatter.main.adapter.TrialGoalsAdapter
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_tiral_goals.goalsRv
import kotlinx.android.synthetic.main.dialog_tiral_goals.ivClose
import kotlinx.android.synthetic.main.dialog_tiral_goals.tvCheck
import kotlinx.android.synthetic.main.dialog_tiral_goals.tvCountdown

/**
 * 试岗优化弹窗
 */
class TrialGoalsDialog() : BaseDialogFragment() {

    private var entity: GoalsEntity? = null

    constructor(entity: GoalsEntity) : this() {
        this.entity = entity
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_tiral_goals, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.86).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false
        tvCountdown.show(entity != null && entity?.countDay!! > 0)
        tvCountdown.text = buildString {
            append("Countdown : ")
            append(entity?.countDay)
            append("d")
        }
        val adapter = TrialGoalsAdapter()
        goalsRv.layoutManager = LinearLayoutManager(context)
        goalsRv.adapter = adapter
        adapter.updateData(entity?.progressList)
        tvCheck.clickWithTrigger {
            SimpleRxBus.post(UIToMPCEvent())
            dismissAllowingStateLoss()
        }

        ivClose.clickWithTrigger {
            dismissAllowingStateLoss()
        }
    }
}