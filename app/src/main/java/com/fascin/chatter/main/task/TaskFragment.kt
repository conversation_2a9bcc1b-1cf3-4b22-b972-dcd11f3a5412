package com.fascin.chatter.main.task

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.RewardEntity
import com.fascin.chatter.bean.TaskEntity
import com.fascin.chatter.bean.TaskIntent
import com.fascin.chatter.bean.TaskWeeklyEntity
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.statusBarHeightForImmersive
import com.iandroid.allclass.lib_common.widgets.RotationHelper
import kotlinx.android.synthetic.main.fragment_task.rl_statusBar
import kotlinx.android.synthetic.main.fragment_task.taskRv
import kotlinx.android.synthetic.main.fragment_task.titleBar
import kotlinx.android.synthetic.main.layout_title_bar.titleBarRightIcon
import kotlinx.android.synthetic.main.layout_title_bar.titleBarTitle

/**
 * @Desc: 任务模块、task历史详情
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
class TaskFragment : BaseUiFragment(), IRvItemAction {

    // 查看历史周任务数据时
    private var taskID: Int = 0
    private var isHistoryPage: Boolean = false
    private var rewardInfo: List<RewardEntity>? = null
    private var viewModel: TaskViewModel? = null
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var refreshing: Boolean = false

    override fun attachLayoutId(): Int {
        return R.layout.fragment_task
    }

    override fun initView(view: View?) {
        super.initView(view)
        initTitleBar()
        initRv()
        // 设置骨骼视图
        setPreView()
        setViewModel()
    }

    private fun initTitleBar() {
        activity?.intent?.getStringExtra(Values.intentJsonParam)?.jsonToObj<TaskIntent>()?.let {
            if (it.taskId > 0) taskID = it.taskId
            isHistoryPage = it.isHistory
            rewardInfo = it.rewardInfo
        }
        if (taskID > 0) {
            rl_statusBar.show(false)
            titleBar.show(false)
            return
        }
        rl_statusBar.statusBarHeightForImmersive(requireContext())
        titleBarTitle.text = getText(R.string.main_tab_task)
        titleBarRightIcon.setImageResource(R.mipmap.ic_refresh)
        titleBarRightIcon.show(taskID == 0)
        titleBarRightIcon.clickWithTrigger {
            // 刷新数据
            if (!refreshing) {
                RotationHelper.startRotation(titleBarRightIcon)
                fetchPageData(true)
            }
        }
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, taskRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    if (!refreshing) fetchPageData(true)
                }

                override fun onFooterRefresh() {

                }
            })
        }
    }

    private fun setPreView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.taskPlaceItemView, this))
        })
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(TaskViewModel::class.java).apply {
            taskInfoLiveData.observe(this@TaskFragment) {
                // 处理数据
                refreshing = false
                if (taskID == 0) RotationHelper.releaseRotation(titleBarRightIcon)
                recyclerViewSupport?.onHeaderRefreshComplete()
                setRvData(it)
            }
            taskInfoErrorLiveData.observe(this@TaskFragment) {
                // 请求失败
                refreshing = false
                if (taskID == 0) RotationHelper.releaseRotation(titleBarRightIcon)
                recyclerViewSupport?.onHeaderRefreshComplete()
                addErrorView()
            }
        }
    }

    /**
     * 获取数据
     */
    override fun fetchPageData(refresh: Boolean) {
        refreshing = true
        viewModel?.getTaskInfo(taskID)
    }

    /**
     * 设置数据
     */
    private fun setRvData(taskEntity: TaskEntity?) {
        if (taskEntity == null) {
            addEmptyView()
            return
        }
        taskEntity.run {
            updateData(ArrayList<BaseRvItemInfo>().also { list ->
                getWeeklyEntity(this).let { curTask ->
                    list.add(BaseRvItemInfo(curTask, AppViewType.taskWeeklyItemView, this))
                }
                if (!isHistoryPage) {
                    history?.let { taskHistorical ->
                        list.add(
                            BaseRvItemInfo(
                                taskHistorical, AppViewType.taskHistoricalItemView, this
                            )
                        )
                    }
                }
            })
        }
    }

    private fun getWeeklyEntity(taskEntity: TaskEntity?): TaskWeeklyEntity {
        return TaskWeeklyEntity().apply {
            taskEntity?.also {
                isHistory = isHistoryPage
                titleInfo = it.task_summary
                settlements = it.settlements
                if (rewardInfo?.isNotEmpty() == true)
                    rewardTableInfo = rewardInfo as ArrayList<RewardEntity>
                it.require?.forEach { child ->
                    child.titleInfo = AppContext.context.getString(R.string.require_item_desc)
                    childEntitys?.add(child)
                }
                it.option?.forEach { child ->
                    child.titleInfo = if (it.optionNeedNum > 0) {
                        String.format(
                            AppContext.context.getString(R.string.option_item_desc),
                            it.optionNeedNum
                        )
                    } else ""
                    childEntitys?.add(child)
                }

            }
        }
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo>?) {
        if (itemTemp?.isNotEmpty() == true) recyclerViewSupport?.updateData(itemTemp, true)
        else addEmptyView()
    }

    /**
     * 数据错误视图
     */
    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    /**
     * 空视图
     */
    private fun addEmptyView() {
        updateData(ArrayList<BaseRvItemInfo>().also { list ->
            list.add(BaseRvItemInfo(EmptyEntity().also {
                it.content = getString(
                    if (taskID > 0) R.string.task_weekly_empty_content
                    else R.string.task_historical_empty_content
                )
                it.icRes = R.mipmap.ic_public_album_empty
            }, AppViewType.comEmptyView, this))
        })
    }

    override fun startRefresh() {
        super.startRefresh()
        fetchPageData(true)
    }

    override fun onRefresh() {
        fetchPageData(true)
    }
}