package com.fascin.chatter.main.match

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.ChatterBindUpdateEntity
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.MatchedUserInfoItem
import com.fascin.chatter.im.UserOnlineHelper
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.EventMatchFilterChange
import com.iandroid.allclass.lib_common.beans.EventNewMatchUpdate
import com.iandroid.allclass.lib_common.beans.EventVipUpdateMatch
import com.iandroid.allclass.lib_common.beans.UIEventClearMatchCountUpdate
import com.iandroid.allclass.lib_common.beans.UIEventNewMatchCountUpdate
import com.iandroid.allclass.lib_common.event.RefreshOnlineUIEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.castObject
import io.rong.imkit.IMCenter
import io.rong.imkit.widget.FixedLinearLayoutManager
import kotlinx.android.synthetic.main.fragment_match.matchRv

open class MatchFragment : BaseUiFragment(), IMatchRvItemAction {

    private var filterConfig: Int = 0
    private var viewModel: MatchViewModel? = null

    var recyclerViewSupport: RecyclerViewSupport? = null
    private var lastId: Int = 0
    private var newMatchCount: Int = 0
    private var isRefreshing: Boolean = false

    // 切回此页面时，是否需要刷新数据
    private var needRefresh: Boolean = false
    private var refreshTime: Long = 0L
    private val REFRESH_INTERVAL = 3 * 60 * 1000L

    private var matchList = ArrayList<MatchedUserInfoItem>()

    override fun initView(view: View?) {
        super.initView(view)
        if (!IMCenter.getInstance().isInitialized) {
            return
        }
        viewModel = ViewModelProvider(this).get(MatchViewModel::class.java)
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, matchRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    // 手动下拉刷新时，更新已读状态，然后readNewMatch回调中获取数据
                    if (newMatchCount > 0) viewModel?.readNewMatch(true)
                    else fetchPageData(true)
                }

                override fun onFooterRefresh() {
                    fetchPageData(false)
                }
            })
        }

        matchRv.setLayoutManager(FixedLinearLayoutManager(activity))
        //骨骼
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                for (i in 0 until 12) {
                    it.add(BaseRvItemInfo(Any(), AppViewType.userMatchPlaceItemView))
                }
            }, isData = false
        )

        viewModel?.chatterMatchedLiveData?.observe(this) {
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            isRefreshing = false
            if (lastId <= 0) matchList.clear()
            if (it.list?.isNotEmpty() == true) matchList.addAll(it.list!!)
            updateData(getMatchRvViewList(matchList), isData = true)
            // 只有第一页数据返回new_match_num
            if (lastId <= 0) newMatchCount = it.new_match_num

            if (lastId > 0 && it?.list.isNullOrEmpty()) {
                recyclerViewSupport?.setCanPullUp(false)
                recyclerViewSupport?.setNoMoreData(true)
            }
            if (it?.list?.isNotEmpty() == true) lastId = it.list?.lastOrNull()?.id!!

            //没有数据
            if (recyclerViewSupport?.hasData() == false) addEmptyView()
        }

        viewModel?.readLiveData?.observe(this) {
            newMatchCount = 0
            // 手动下拉刷新时，先更新已读状态，然后获取数据
            if (it) fetchPageData(true)
        }

        viewModel?.sayHiSuccessLiveData?.observe(this) {
            if (it.isNullOrEmpty() || it == "0") {
                startRefresh()
            } else {
                sayHiSuccess(it, true)
            }
            ToastUtils.showToast(R.string.text_sayhi_success)
        }

        viewModel?.chatterMatchedErrorLiveData?.observe(this) {
            isRefreshing = false
            recyclerViewSupport?.onHeaderRefreshComplete()
            recyclerViewSupport?.onFooterRefreshComplete()
            if (recyclerViewSupport?.hasData() == false) {
                //没有数据
                addErrorView()
            }
        }

        viewModel?.sayHiErrorLiveData?.observe(this) {
            if (it.userId?.isNotEmpty() == true) {
                sayHiSuccess(it.userId!!, false)
            }
            ToastUtils.showToast(it.desc)
        }

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(EventNewMatchUpdate::class) {
            fetchPageData(true)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIEventClearMatchCountUpdate::class) {
            // 切换tab到match时，更新已读状态
            viewModel?.readNewMatch()
            if (AppModule.isMatchTabShow) {
                val interval = System.currentTimeMillis() - refreshTime
                if (needRefresh || interval > REFRESH_INTERVAL) {
                    fetchPageData(true)
                } else recyclerViewSupport?.updateView()
            }

        })

        //解绑&&绑定事件
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ChatterBindUpdateEntity::class) {
            fetchPageData(true)
        })

        // user上线更新UI
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RefreshOnlineUIEvent::class) {
            if (it.userIds?.isNotEmpty() == true) updateUserOnline(it.userIds, 1)
        })
        // userVip状态更新
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(EventVipUpdateMatch::class) {
            if (it.vipUidList?.isNotEmpty() == true) updateUserVip(it.vipUidList!!, it.vip)
        })
    }

    /**
     * 筛选
     */
    fun clickRightMore() {
        FilterDialog(filterConfig) {
            filterConfig = it
            SimpleRxBus.post(EventMatchFilterChange())
            fetchPageData(true)
        }.show(
            childFragmentManager, FilterDialog::javaClass.name
        )
    }

    private fun sayHiSuccess(userId: String, success: Boolean) {
        recyclerViewSupport?.infos?.also { rvItems ->
            val index = rvItems.indexOfFirst { item ->
                item.viewType == AppViewType.chatterMatchedItemView && item.data?.castObject<MatchedUserInfoItem>()?.im_id == userId
            }
            if (index != -1) {
                rvItems[index].data?.castObject<MatchedUserInfoItem>()?.also {
                    it.greeted = if (success) 1 else 0
                    it.greeting = false
                }

                // 需要sayHi过的用户显示在会话列表时，移除item
                if (AppController.isChatShowMatch()) {
                    rvItems.removeAt(index)
                    val findItem = matchList.find { it.im_id == userId }
                    if (findItem != null) matchList.remove(findItem)
                    recyclerViewSupport?.updateView()
                    // 更新New tab上的小点点
                    SimpleRxBus.post(UIEventNewMatchCountUpdate(newMatchCount))
                    if (recyclerViewSupport?.hasData() == false) addEmptyView()
                } else {
                    recyclerViewSupport?.updateViewByPosition(index)
                }
            }
        }
    }

    private fun getMatchRvViewList(list: ArrayList<MatchedUserInfoItem>?): ArrayList<BaseRvItemInfo> {
        val itemViewList = ArrayList<BaseRvItemInfo>()

        list?.also {
            for (item in it) {
                itemViewList.add(BaseRvItemInfo(item, AppViewType.chatterMatchedItemView, this))
            }
        }
        return itemViewList
    }

    override fun onResume() {
        super.onResume()
        if (AppModule.isMatchTabShow) {
            val interval = System.currentTimeMillis() - refreshTime
            if (needRefresh || interval > REFRESH_INTERVAL) {
                fetchPageData(true)
            } else recyclerViewSupport?.updateView()
        }
    }

    override fun fetchPageData(refresh: Boolean) {
        needRefresh = false
        refreshTime = System.currentTimeMillis()
        if (isRefreshing) return
        isRefreshing = true
        if (refresh) {
            lastId = 0
            recyclerViewSupport?.setNoMoreData(false)
            recyclerViewSupport?.setCanPullUp(true)
        }
        getChatterMatchedList()
    }

    private fun getChatterMatchedList() {
        viewModel?.getChatterMatchedList(
            if (filterConfig and FilterDialog.FlagOpenOnline > 0) 1 else 0,
            if (filterConfig and FilterDialog.FlagOpenMember > 0) 1 else 0,
            lastId,
            if (AppController.isChatShowMatch()) 0 else 1
        )
    }

    private fun updateUserOnline(onlineUids: ArrayList<String>, isOnline: Int) {
        recyclerViewSupport?.infos?.also { rvItems ->
            var updatad = false
            for (uid in onlineUids) {
                val index = rvItems.indexOfFirst { item ->
                    item.viewType == AppViewType.chatterMatchedItemView
                            && item.data?.castObject<MatchedUserInfoItem>()?.im_id?.contains(uid) == true
                }
                if (index != -1) {
                    rvItems[index].data?.castObject<MatchedUserInfoItem>()?.u_online = isOnline
                    updatad = true
                }
            }
            if (updatad) updateData(rvItems, isData = true)
        }
    }

    private fun updateUserVip(vipUidList: ArrayList<String>, vipStatus: Int) {
        recyclerViewSupport?.infos?.also { rvItems ->
            var updatad = false
            for (uid in vipUidList) {
                val index = rvItems.indexOfFirst { item ->
                    item.viewType == AppViewType.chatterMatchedItemView && item.data?.castObject<MatchedUserInfoItem>()?.im_id == uid
                }
                if (index != -1) {
                    rvItems[index].data?.castObject<MatchedUserInfoItem>()?.vip = vipStatus
                    updatad = true
                }
            }
            if (updatad) recyclerViewSupport?.updateView()
        }
    }

    private fun updateData(dataList: ArrayList<BaseRvItemInfo>, clearData: Boolean = true, isData: Boolean) {
        if (isData) {
            sortData(matchList)
            recyclerViewSupport?.updateData(getMatchRvViewList(matchList), true)
        } else {
            recyclerViewSupport?.updateData(dataList, clearData)
        }
    }

    private fun sortData(dataList: ArrayList<MatchedUserInfoItem>?) {
        dataList?.sortWith(
            compareByDescending<MatchedUserInfoItem> {
                // 先按在线排
                UserOnlineHelper.getOnline(it.im_id)
            }.thenByDescending {
                // 再按活跃状态排
                UserOnlineHelper.isActive(it.im_id, it.lastActiveTime)
            }
        )
    }

    /**
     * 产生了新的聊天时，判断是否需要刷新数据
     */
    fun chatChange(targetId: String) {
        if (isRefreshing) return
        if (AppModule.isMatchTabShow) {
            fetchPageData(true)
        } else {
            if (!needRefresh) {
                needRefresh = matchList.any { it.im_id == targetId }
            }
        }
    }

    override fun attachLayoutId(): Int {
        return R.layout.fragment_match
    }

    override fun onSayHi(userId: String) {
        viewModel?.sayHi(userId)
    }

    override fun onRefresh() {
        fetchPageData(true)
    }

    private fun addErrorView() {
        updateData(
            ArrayList<BaseRvItemInfo>().also {
                it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
            }, true, isData = false
        )
    }

    private fun addEmptyView() {
        updateData(
            ArrayList<BaseRvItemInfo>().also { list ->
                list.add(BaseRvItemInfo(EmptyEntity().also {
                    it.title = getString(R.string.page_no_people)
                    it.content = getString(
                        if (filterConfig == 0)
                            R.string.page_no_people_desc
                        else
                            R.string.match_empty_desc_filter
                    )
                    if (filterConfig != 0) {
                        it.btnTwoTitle = "Clear filters"
                        it.setOnClick { index ->
                            if (index == EmptyEntity.CLICK_TWO_BTN && !isRefreshing) {
                                // 清空过滤,
                                filterConfig = 0
                                SimpleRxBus.post(EventMatchFilterChange())
                                fetchPageData(true)
                            }
                        }
                    }
                }, AppViewType.comEmptyView, this))
            }, true, isData = false
        )
    }

    /**
     * 判断是否有tab标签
     * @return -1 没有标签 0 有在线的 1 有新match
     */
    fun hasTabTag(): Int {
        if (newMatchCount > 0) return 1
        matchList.forEach {
            if (UserOnlineHelper.isOnline(it.im_id)) {
                return 0
            }
        }
        return -1
    }

    override fun startRefresh() {
        super.startRefresh()
        fetchPageData(true)
    }
}