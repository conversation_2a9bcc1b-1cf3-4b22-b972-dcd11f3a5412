package com.fascin.chatter.main.chats

import android.view.View
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.MixListFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.core.OnClickListener
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
 * @Desc: 有会话记录的用户列表
 * @Created: Quan
 * @Date: 2023/12/13
 */
class LongLostFragment : MixListFragment(), OnClickListener {

    // 当前model id
    private var modelId: String = ""

    // all chats中的所有会话id,用于过滤
    private var targetIds: ArrayList<String>? = ArrayList()

    // model对应的数据缓存
    private var dataMap: HashMap<String, ArrayList<ExploreUserEntity>?>? = HashMap()
    private var isInCurTab: Boolean = false
    private var viewModel: ChatsViewModel? = null
    private var listener: (Boolean) -> Unit = {}
    private var clickChat = ""
    private var chatChange = false

    override fun initView(view: View?) {
        super.initView(view)
        if (mixPageEntity == null) return
        setRv()
        setPreView()
    }

    private fun setRv() {
        // 此页面数据一次性加载完成，无需刷新与加载更多
        recyclerViewSupport?.setCanPullDown(false)
        recyclerViewSupport?.setCanPullUp(false)
    }

    override fun fetchPageData(refresh: Boolean) {

    }

    override fun requestData(refresh: Boolean) {
        if (modelId.isEmpty()) return
        if (dataMap?.containsKey(modelId) == true) {
            updatePageData()
        } else {
            if (modelId.isNotEmpty())
                viewModel?.getFindLostList(modelId)
        }
    }

    override fun showToTopForNum(): Int {
        return 3
    }

    private fun setPreView() {
        setData(ArrayList<BaseRvItemInfo?>().also { list ->
            repeat(7) {
                list.add(BaseRvItemInfo(Any(), AppViewType.findChatsPlaceItemView, this))
            }
        })
    }

    private fun setData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) recyclerViewSupport?.updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    override fun addEmptyView() {
        kotlin.runCatching {
            if (context != null) {
                val emptyEntity = EmptyEntity().also {
                    it.content = context.getString(R.string.page_nodata_lost)
                    it.icRes = R.mipmap.ic_msg_setting_nodata
                    it.setOnClick {
                        requestData(true)
                    }
                }

                emptyEntity.also { data ->
                    updateData(ArrayList<BaseRvItemInfo?>().also {
                        it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
                    }, true)
                }
            }
        }
    }

    override fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        }, true)
    }

    /**
     * 数据发生变化，刷新页面
     */
    private fun updatePageData(isError: Boolean = false) {
        onRefreshComplete()
        val filterData = filterData(dataMap?.get(modelId))
        if (isInCurTab) {
            if (isError) {
                addErrorView()
            }
            notifyDataChange(filterData)
        }
    }

    fun setViewModel(viewModel: ChatsViewModel) {
        this.viewModel = viewModel
        this.viewModel?.findLostLiveData?.observeForever {
            it?.let {
                dataMap?.put(it.modelId, it.list)
            }
            updatePageData()
        }
        this.viewModel?.findLostError?.observeForever {
            if (it == modelId && dataMap?.containsKey(modelId) != true) {
                updatePageData(true)
            }
        }
    }

    fun setListener(listener: (Boolean) -> Unit) {
        this.listener = listener
    }

    fun pageChange(modelId: String, isVisible: Boolean, targetIds: ArrayList<String>?) {
        this.modelId = modelId
        this.isInCurTab = isVisible
        this.targetIds = targetIds
        if (isVisible) {
            // 刷新列表
            setPreView()
        }
        // 获取数据
        requestData(true)
    }

    /**
     * 添加或移除targetId(老关系产生了新消息记录、删除会话)
     */
    fun targetIdChange(targetId: String?, addLost: Boolean) {
        if (targetId.isNullOrEmpty()) return
        targetIds?.also {
            if (addLost) {
                if (it.contains(targetId)) {
                    it.remove(targetId)
                } else {
                    return
                }
            } else {
                if (!it.contains(targetId)) {
                    it.add(targetId)
                    chatChange = targetId == clickChat
                } else {
                    return
                }
            }
            updatePageData()
        }
    }

    fun onlineChange() {
        dataMap?.forEach {
            it.value?.forEach { user ->
                val newOffTime = UserOnlineHelper.newOffTime(user.imId)
                if (newOffTime > 0) {
                    user.lastActiveTime = newOffTime
                }
            }
        }
        val filterData = filterData(dataMap?.get(modelId))
        if (isInCurTab) {
            notifyDataChange(filterData)
        }
    }

    /**
     * 设置列表数据
     */
    private fun notifyDataChange(chatList: ArrayList<ExploreUserEntity>?) {
        if (chatList?.isNotEmpty() == true)
            sortData(chatList)
        setData(
            ArrayList<BaseRvItemInfo?>().also {
                chatList?.forEach { entity ->
                    it.add(BaseRvItemInfo(entity, AppViewType.findChatsItemView, this))
                }
            }, true
        )

    }

    private fun filterData(chatList: ArrayList<ExploreUserEntity>?): ArrayList<ExploreUserEntity>? {
        // 筛选掉all chat中已经存在的
        val filter = chatList?.filter { entity -> targetIds?.any { it == entity.imId } != true }
        // 是否存在在线的
        listener.invoke(filter?.any { UserOnlineHelper.isOnline(it.imId) } == true)
        return if (filter?.isNotEmpty() == true) filter as ArrayList<ExploreUserEntity>? else null
    }

    /**
     * 排序
     */
    private fun sortData(chatList: ArrayList<ExploreUserEntity>?) {
        chatList?.sortWith { entity1, entity2 ->
            if (UserOnlineHelper.getOnline(entity1.imId) != UserOnlineHelper.getOnline(entity2.imId)) {
                // 按在线状态降序排
                UserOnlineHelper.getOnline(entity2.imId).compareTo(UserOnlineHelper.getOnline(entity1.imId))
            } else if (UserOnlineHelper.isActive(entity1.imId, entity1.lastActiveTime)
                != UserOnlineHelper.isActive(entity2.imId, entity2.lastActiveTime)
            ) {
                // 按活跃状态降序排
                val active2 = if (UserOnlineHelper.isActive(entity2.imId, entity2.lastActiveTime)) 1 else 0
                val active1 = if (UserOnlineHelper.isActive(entity1.imId, entity1.lastActiveTime)) 1 else 0
                active2.compareTo(active1)
            } else if (entity1.platformUnlockNum != entity2.platformUnlockNum) {
                // 按总解锁数量降序排
                entity2.platformUnlockNum.compareTo(entity1.platformUnlockNum)
            } else if (entity1.unlockNum != entity2.unlockNum) {
                // 按解锁当前model数量降序排
                entity2.unlockNum.compareTo(entity1.unlockNum)
            } else {
                // 按最后聊天时间降序排
                entity2.lastChatTime.compareTo(entity1.lastChatTime)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        // 从此页面点击列表进入chat，并且移到chat了，返回此页面时提示告知主播
        if (isInCurTab && chatChange) {
            ToastUtils.showToast(R.string.conversation_lost_in_chat_tip)
        }
        clickChat = ""
        chatChange = false
    }

    override fun onClick(id: String) {
        if (isInCurTab && id.isNotEmpty())
            clickChat = id
    }
}