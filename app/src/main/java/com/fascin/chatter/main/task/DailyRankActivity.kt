package com.fascin.chatter.main.task

import android.os.Bundle
import com.fascin.chatter.R
import com.fascin.chatter.main.HomeConfig
import com.iandroid.allclass.lib_common.base.FasBaseActivity
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.formatSecondsToHm
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import com.iandroid.allclass.lib_common.views.CommonCloseVerticalDialog
import kotlinx.android.synthetic.main.activity_daily_rank.newbieLayout
import kotlinx.android.synthetic.main.activity_daily_rank.rankBack
import kotlinx.android.synthetic.main.activity_daily_rank.rankRight
import kotlinx.android.synthetic.main.activity_daily_rank.tabGroup
import kotlinx.android.synthetic.main.activity_daily_rank.tabNewbie
import kotlinx.android.synthetic.main.activity_daily_rank.tabW2
import kotlinx.android.synthetic.main.activity_daily_rank.tvTime
import kotlinx.android.synthetic.main.activity_daily_rank.w2Layout

class DailyRankActivity : FasBaseActivity() {

    private var isNewUser = false
    private var curTab: Int = 0 // 0: newbie 1: W2
    private var rule: String = ""
    private var mNewbieFragment: RankFragment? = null
    private var mW2Fragment: RankFragment? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_daily_rank)
        initViewUI()
        setListener()
        if (isNewUser) tabGroup.check(R.id.tabNewbie)
    }

    private fun initViewUI() {
        isNewUser = HomeConfig.isNewChatter == 1
        mW2Fragment = supportFragmentManager.findFragmentById(R.id.w2Fragment) as RankFragment
        tabGroup.show(isNewUser)
        newbieLayout.show(isNewUser)
        w2Layout.show(!isNewUser)
        if (isNewUser) {
            mNewbieFragment = supportFragmentManager.findFragmentById(R.id.newbieFragment) as RankFragment
            mNewbieFragment?.setFragmentTab(1)
        }
        mW2Fragment?.setFragmentTab(0)
    }

    private fun setListener() {
        rankBack.clickWithTrigger {
            finish()
        }

        tabGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.tabNewbie -> {
                    curTab = 0
                    tabNewbie.textColorResource = R.color.white
                    tabW2.textColorResource = com.iandroid.allclass.lib_basecore.R.color.cl_595959
                    w2Layout.show(false)
                    newbieLayout.show(true)
                }

                R.id.tabW2 -> {
                    curTab = 1
                    tabW2.textColorResource = R.color.white
                    tabNewbie.textColorResource = com.iandroid.allclass.lib_basecore.R.color.cl_595959
                    newbieLayout.show(false)
                    w2Layout.show(true)
                }
            }
        }

        rankRight.clickWithTrigger {
            CommonCloseVerticalDialog.Builder()
                .showCloseBtn(true)
                .setTitle("Rule")
                .setContext(rule)
                .setConfirm("Ok, I got it") {}
                .create().show(supportFragmentManager, CommonCloseVerticalDialog::class.java.name)
        }
    }

    fun setRuleTime(rule: String?, time: Long) {
        this.rule = rule.orEmpty()
        rankRight.show(rule?.isNotEmpty() == true)
        tvTime.show(time > 0)
        tvTime.text = formatSecondsToHm(time)
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return false
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }
}