package com.fascin.chatter.main.chats.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.chats.ChatsConfig
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.utils.ConnectFlagInvisible
import com.fascin.chatter.utils.ConnectFlagMuteMsg
import com.fascin.chatter.utils.ConnectFlagNoOnline
import com.fascin.chatter.utils.SUDateUtils
import com.fascin.chatter.utils.isFlagEnabled
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.core.OnClickListener
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivChatTag
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivContactTag
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivDisturb
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivInvisible
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivModelPortrait
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivOffOnline
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivOnline
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivPortrait
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivUnlockLvl
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivVip
import kotlinx.android.synthetic.main.itemview_find_chat.view.ivWarning
import kotlinx.android.synthetic.main.itemview_find_chat.view.llDate
import kotlinx.android.synthetic.main.itemview_find_chat.view.tagUnlock
import kotlinx.android.synthetic.main.itemview_find_chat.view.tvDate
import kotlinx.android.synthetic.main.itemview_find_chat.view.tvTitle

/**
 * FindChat Item
 * @Created: QuanZH
 * @Date: 2023/9/20
 */
@RvItem(id = AppViewType.findChatsItemView, spanCount = 1)
class FindChatItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_find_chat
    }

    override fun initView(context: Context?, view: View?) {

    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            ivPortrait?.loadImage(AppContext.context, entity.avatarUrl)
            ivModelPortrait?.loadImage(
                AppContext.context,
                ChatsConfig.allModelIdAndHead[getModelId(entity.imId)].orEmpty()
            )
            ivInvisible?.show(isFlagEnabled(entity.chatFlag, ConnectFlagInvisible))
            ivOnline?.show(
                UserOnlineHelper.isOnline(entity.imId)
                        || UserOnlineHelper.isActive(entity.imId, entity.lastActiveTime)
            )
            // 设置在线状态图标
            if (UserOnlineHelper.isOnline(entity.imId)) {
                ivOnline.setImageResource(R.drawable.shape_user_online)
            } else {
                ivOnline.setImageResource(R.drawable.shape_user_active)
            }
            ivOffOnline?.show(isFlagEnabled(entity.chatFlag, ConnectFlagNoOnline))
            ivVip?.setVipStatus(entity.vip)
            ivWarning?.show(entity.isCantPayUser())
            ivDisturb?.show(isFlagEnabled(entity.chatFlag, ConnectFlagMuteMsg))
            tvTitle?.text = entity.nickname
            val drawable = AppController.getUnlockLvl(entity.platformUnlockNum)
            ivUnlockLvl?.show(AppController.showUnlockLvl() && drawable > 0)
            ivUnlockLvl?.setImageResource(drawable)
            tagUnlock?.show(entity.unlockNum > 0)
            tagUnlock?.text = buildString {
                append(getStringById(R.string.conversation_tab_unlock))
                append(" ")
                append(if (entity.unlockNum > 0) entity.unlockNum.toString() else "")
            }
            ivChatTag?.setChatFlag(entity.msgFlag)
            ivContactTag?.setTagUI(entity.imId, entity.contactTag, entity.nickname)
            tvDate?.text =
                SUDateUtils.getTimeDifference(if (entity.lastChatTime > 0) entity.lastChatTime.times(1000) else 0L)
            llDate?.show(entity.lastChatTime > 0 && tvDate.text.isNotEmpty())
            ivWarning?.clickWithTrigger {
                ToastUtils.showToast(R.string.im_cant_pay_tip)
            }
            clickWithTrigger {
                RouteUtils.routeToConversationActivity(
                    context, Conversation.ConversationType.PRIVATE, entity.imId
                )
                getCallback()?.onClick(entity.imId)
                AppRepository.eventTrace(EventKey.K_ll_chat_c) {
                    "targetId" to entity.imId
                }
            }
        }
    }

    private fun getItemData(): ExploreUserEntity? = data?.castObject<ExploreUserEntity>()

    override fun getItemOffsets(
        parent: RecyclerView,
        view: View?,
        outRect: Rect,
        position: Int
    ): Boolean {
        if (position == getItemCounts() - 1) {
            outRect.bottom = 16.toPx
        }
        return true
    }

    private fun getCallback(): OnClickListener? = callBack?.castObject<OnClickListener>()

    private fun getModelId(imUserId: String): String {
        val targetIdIndex = imUserId.indexOf("_")
        if (targetIdIndex > 0) {
            return imUserId.substring(0, targetIdIndex)
        }
        return ""
    }
}