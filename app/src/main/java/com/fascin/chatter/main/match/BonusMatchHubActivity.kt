package com.fascin.chatter.main.match

import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.FindChatIntent
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.component.player.SUHttpCacheServer
import com.fascin.chatter.component.player.SUMediaPlayer
import com.fascin.chatter.component.player.SURenderCallback
import com.fascin.chatter.component.player.SUVolumeEvent
import com.fascin.chatter.component.views.swipe.SwipeItemAnimation
import com.fascin.chatter.component.views.swipe.SwipeLayoutManager
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.adapter.BonusMatchHubAdapter
import com.fascin.chatter.main.adapter.BonusModelListAdapter
import com.fascin.chatter.main.adapter.OnItemRemovedListener
import com.fascin.chatter.main.match.view.MomentInputViewImpl
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.beans.ChatAtInfo
import com.iandroid.allclass.lib_common.beans.MediaEntity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.event.RefreshOnlineUIEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.SPConstants
import com.iandroid.allclass.lib_common.utils.SPUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithMargin
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.views.input.MultiInputView
import kotlinx.android.synthetic.main.activity_bonus_match_hub.bonusMatchLeft
import kotlinx.android.synthetic.main.activity_bonus_match_hub.bonusMatchLeftBack
import kotlinx.android.synthetic.main.activity_bonus_match_hub.bonusMatchTitleView
import kotlinx.android.synthetic.main.activity_bonus_match_hub.bonusStatusPlaceView
import kotlinx.android.synthetic.main.activity_bonus_match_hub.bonus_active_touch_view
import kotlinx.android.synthetic.main.activity_bonus_match_hub.btnBonusOk
import kotlinx.android.synthetic.main.activity_bonus_match_hub.chatInputContainer
import kotlinx.android.synthetic.main.activity_bonus_match_hub.flashChatRootView
import kotlinx.android.synthetic.main.activity_bonus_match_hub.llBonusNoUser
import kotlinx.android.synthetic.main.activity_bonus_match_hub.llNoBonusLeft
import kotlinx.android.synthetic.main.activity_bonus_match_hub.rlBonusFlashChat
import kotlinx.android.synthetic.main.activity_bonus_match_hub.rlBonusLike
import kotlinx.android.synthetic.main.activity_bonus_match_hub.rvBonusMatchHubCard
import kotlinx.android.synthetic.main.activity_bonus_match_hub.rvBonusModel
import kotlinx.android.synthetic.main.item_medias_image.view.medias_image_view
import kotlinx.android.synthetic.main.item_medias_image.view.medias_video_container
import kotlinx.android.synthetic.main.itemview_bonus_match_hub.view.bonusUserMedias

/**
 *  @author: LXL
 *  @description: 定向建立（新老主播）
 *  @date: 2024/4/17 10:53
 */
class BonusMatchHubActivity : ChatterBaseActivity(), OnItemRemovedListener {
    var chatMultiInput: MomentInputViewImpl? = null
    private var bonusMatchHubAdapter: BonusMatchHubAdapter? = null
    private var modelListAdapter: BonusModelListAdapter? = null
    private var modelList: ArrayList<ModelUserEntity>? = ArrayList()
    private var modelId: String = ""
    private var pauseTime: Long = System.currentTimeMillis()
    private val suMediaPlayer by lazy { SUMediaPlayer(this) }
    private val viewModel by lazy {
        ViewModelProvider(this, MatchViewModel.ViewModeFactory())[MatchViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_bonus_match_hub)
        viewModel?.bonusMatchHubList()
    }

    override fun initBaseContent() {
        super.initBaseContent()
        AppModule.userActive()
        showTitleBar(false)
        immersiveStatusBarWithMargin(bonusMatchTitleView)
        bonusMatchLeftBack.setOnClickListener {
            finish()
        }
        parseJsonParams<FindChatIntent>()?.let {
            modelList = it.list
        }
        chatMultiInput = MomentInputViewImpl(chatInputContainer)
        initChatMultiInput()
        bonusMatchHubCard()
        selectModel()

        bonus_active_touch_view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    AppModule.userActive()
                }
            }
            false
        }

        viewModel?.bonusMatchHubLiveData?.observe(this) {
            bonusStatusPlaceView.show(false)
            bonusMatchLeft.text = "${it.leftTime} left"
            if (it.isSend == 0 || it.matchList.isEmpty()) {
                if (it.leftTime > 0) {
                    llBonusNoUser.show(true)
                    llNoBonusLeft.show(false)
                } else {
                    llBonusNoUser.show(false)
                    llNoBonusLeft.show(true)
                }
            } else {
                it.matchList.filter { match -> match.online == 1 }.let { onlineList ->
                    if (onlineList?.isNotEmpty() == true) {
                        // 将在线状态存到UserOnlineHelper中
                        UserOnlineHelper.usersOnlineChange(
                            onlineList.map { item -> item.userID }
                                .toMutableList() as ArrayList<String>, 1
                        )
                    }
                }
                bonusMatchHubAdapter?.updateData(it.matchList)
                rlBonusLike.show(true)
                rlBonusFlashChat.show(true)
            }
        }
        viewModel?.bonusMatchHubErrorLiveData?.observe(this) {
            rlBonusLike.show(false)
            rlBonusFlashChat.show(false)
        }

        // user上线更新UI
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RefreshOnlineUIEvent::class) {
            bonusMatchHubAdapter?.notifyDataSetChanged()
        })
    }

    /**
     * 定向建联卡片
     */
    private fun bonusMatchHubCard() {
        bonusMatchHubAdapter = BonusMatchHubAdapter()
        rvBonusMatchHubCard.isNestedScrollingEnabled = false
        rvBonusMatchHubCard.layoutManager = SwipeLayoutManager {

        }
        rvBonusMatchHubCard.adapter = bonusMatchHubAdapter
        bonusMatchHubAdapter?.setOnItemRemovedListener(this)

        //Like
        rlBonusLike.clickWithTrigger {
            setLikeAndFlashChatEnable(false)
            bonusMatchHubAdapter?.getUserId()?.let { userId ->
                if (userId.isNotEmpty()) {
                    viewModel.bonusMatchHubChoose(userId, modelId, 2)
                }
            }
            AppRepository.eventTrace(EventKey.bmh_like_c)
        }
        //Flash Chat
        rlBonusFlashChat.clickWithTrigger {
            setLikeAndFlashChatEnable(false)
            onMultiInputVisible(true)
            AppRepository.eventTrace(EventKey.bmh_flash_chat_c)
        }

        btnBonusOk.clickWithTrigger {
            finish()
        }

        viewModel?.bonusHubChooseLiveData?.observe(this) {
            if (it.error.isNotEmpty()) {   //用户被3个主播建联完，提示不能在建联了
                ToastUtils.showCenterToast(it.error)
            }
            setLikeAndFlashChatEnable(true)
            removeCurrentCard()
            bonusMatchLeft.text = "${it.leftTime} left"
            SimpleRxBus.post(it)
            if (it.leftTime == 0) {
                llBonusNoUser.show(false)
                llNoBonusLeft.show(true)
            }
        }
        viewModel?.bonusHubChooseErrorLiveData?.observe(this) {
            setLikeAndFlashChatEnable(true)
            ToastUtils.showCenterToast(it)
        }

        viewModel?.compositeDisposable?.add(
            SimpleRxBus.observe(SUVolumeEvent::class) {
                suMediaPlayer.setVolume(it.volume)
                getFrontItemView()?.bonusUserMedias?.showMuteView()
            }
        )
    }

    private fun removeCurrentCard() {
        val frontView = getFrontItemView() ?: return
        SwipeItemAnimation(
            frontView = frontView,
            backView = getBackItemView(),
            exploredoneView = null,
            direction = ItemTouchHelper.RIGHT
        ) {
            bonusMatchHubAdapter?.removeItem(0)
            if (bonusMatchHubAdapter?.getBonusUserData().isNullOrEmpty()) {
                rlBonusLike.show(false)
                rlBonusFlashChat.show(false)
                bonusStatusPlaceView.show(true)
                viewModel.bonusMatchHubList()
            }
        }.play()
    }


    private fun getBackItemView(): View? {
        return rvBonusMatchHubCard?.findViewHolderForAdapterPosition(1)?.itemView
    }

    /**
     * 当前展示的itemView
     */
    private fun getFrontItemView(): View? {
        return rvBonusMatchHubCard?.findViewHolderForAdapterPosition(0)?.itemView
    }

    private fun setLikeAndFlashChatEnable(isEnable: Boolean) {
        rlBonusLike.isEnabled = isEnable
        rlBonusFlashChat.isEnabled = isEnable
    }

    /**
     * 选择主播
     */
    private fun selectModel() {
        modelListAdapter = BonusModelListAdapter(this)
        rvBonusModel.adapter = modelListAdapter
        rvBonusModel.layoutManager = LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false)
        val filteredList = modelList?.filter { modelList ->
            modelList.status != 1
        }
        if (!filteredList.isNullOrEmpty()) {
            modelListAdapter?.updateData(filteredList)
            val userIdToFind = SPUtils.getString(this, SPConstants.KEY_SELECT_MODEL_ID)
            val position = filteredList.indexOfFirst { it.userId == userIdToFind }
            val resultPosition = if (position != -1) position else 0

            modelListAdapter?.setSelectedItem(resultPosition)

            val layoutManager = rvBonusModel.layoutManager as LinearLayoutManager
            layoutManager.scrollToPositionWithOffset(resultPosition - 2, 0)

            modelId = filteredList[resultPosition].userId
        }
        modelListAdapter?.setOnItemClickListener { modelId, position ->
            modelListAdapter?.setSelectedItem(position)
            SPUtils.put(this, SPConstants.KEY_SELECT_MODEL_ID, modelId)
            this.modelId = modelId
        }
    }

    private fun flashChat(msg: String) {
        onMultiInputVisible(false)
        bonusMatchHubAdapter?.getUserId()?.let {
            if (it.isNotEmpty()) {
                viewModel.bonusMatchHubChoose(it, modelId, 3, msg)
            }
        }
    }

    /**
     * 5分钟自动刷新
     */
    private fun startAutoRefresh() {
        if (System.currentTimeMillis() - pauseTime > (5 * 60 * 1000)) {
            rlBonusLike.show(false)
            rlBonusFlashChat.show(false)
            bonusStatusPlaceView.show(true)
            viewModel?.bonusMatchHubList()
        }
    }

    override fun onResume() {
        super.onResume()
        suMediaPlayer.start()
        startAutoRefresh()
    }

    override fun onPause() {
        super.onPause()
        suMediaPlayer.pause()
        pauseTime = System.currentTimeMillis()
    }

    private fun onMultiInputVisible(visible: Boolean) {
        if (visible) {
            chatMultiInput?.popup()
        } else {
            chatMultiInput?.dismiss()
        }
    }

    private fun initChatMultiInput() {
        flashChatRootView.setOnClickListener {
            onMultiInputVisible(false)
        }
        chatMultiInput?.setOutputInterval(3)
        chatMultiInput?.setCallback(object : MultiInputView.Callback {
            override fun onInputDismiss() {
                onMultiInputVisible(false)
                chatMultiInput?.itemView.show(false)
                setLikeAndFlashChatEnable(true)
            }

            override fun onInputPop() {
                chatMultiInput?.itemView.show(true)
            }

            override fun onInputHeightChanged(h: Int) {
                if (h == 0) {
                    chatMultiInput?.itemView.show(false)
                    setLikeAndFlashChatEnable(true)
                }
            }

            override fun onOutputText(
                content: String,
                chatAtList: List<ChatAtInfo>?,
                chatAtInfo: ChatAtInfo?
            ) {
                if (!content.isNullOrEmpty()) {
                    flashChat(content)
                }
            }

            override fun onOutputPicture() {
            }

            override fun onOutputFace(resId: String) {
            }

            override fun onShowImgSendOption() {
            }
        })

        chatMultiInput?.itemView.run {
            show(false)
        }
    }

    override fun immersiveIntoStatusBar(): Boolean {
        return true
    }

    override fun isShowTitleBar(): Boolean {
        return false
    }

    override fun onItemMediaChanged(position: Int, mediaEntity: MediaEntity) {
        if (position > 0) return
        val frontView = getFrontItemView() ?: return
        val mediasView = frontView.bonusUserMedias.getCurrentView() ?: return
        if (mediaEntity.type == 1) {
            mediasView.medias_image_view.show(false)
            suMediaPlayer.attach(mediasView.medias_video_container, object : SURenderCallback {
                override fun onRenderStart() {
                    mediasView.medias_image_view.show(false)
                    mediasView.medias_video_container.alpha = 1F
                }
            })
            suMediaPlayer.setDataSource(SUHttpCacheServer.getProxyUrl(mediaEntity.url))
            mediasView.post { mediasView.medias_image_view.show(true) }
            frontView.bonusUserMedias.showMuteView()
        } else {
            suMediaPlayer.detach()
            frontView.bonusUserMedias.hideMuteView()
        }
    }

    override fun onMuteTapped() {

    }

}