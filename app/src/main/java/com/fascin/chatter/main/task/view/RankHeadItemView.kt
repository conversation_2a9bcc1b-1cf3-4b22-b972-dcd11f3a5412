package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.DailyRankEntity
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textSpanValue
import kotlinx.android.synthetic.main.itemview_rank_head.view.llSelf
import kotlinx.android.synthetic.main.itemview_rank_head.view.trv1
import kotlinx.android.synthetic.main.itemview_rank_head.view.trv2
import kotlinx.android.synthetic.main.itemview_rank_head.view.trv3
import kotlinx.android.synthetic.main.itemview_rank_head.view.tvSelfAmount
import kotlinx.android.synthetic.main.itemview_rank_head.view.tvSelfName
import kotlinx.android.synthetic.main.itemview_rank_head.view.tvSelfPPVCount
import kotlinx.android.synthetic.main.itemview_rank_head.view.tvSelfRank
import kotlinx.android.synthetic.main.itemview_rank_head.view.tvSelfSurpass

/**
 * @Desc: 排行榜前三和自己信息 item
 * @Created: QuanZH
 * @Date: 2024/12/6
 */
@RvItem(id = AppViewType.rankHeadItemView, spanCount = 1)
class RankHeadItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_rank_head
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            if (entity.rankList.isNotEmpty()) trv1.setData(entity.rankList[0])
            if (entity.rankList.size > 1) trv2.setData(entity.rankList[1])
            if (entity.rankList.size > 2) trv3.setData(entity.rankList[2])
            trv2.show(entity.rankList.size > 1, true)
            trv3.show(entity.rankList.size > 2, true)
            llSelf.show(entity.selfRankInfo != null)
            entity.selfRankInfo?.let { self ->
                tvSelfRank.text = if (self.rank in 1..10) self.rank.toString() else "-"
                tvSelfName.text = UserController.getUserId()
                tvSelfAmount.show(entity.showReward == 1)
                tvSelfPPVCount.text = self.ppvCount.toString()
                tvSelfAmount.text = self.rewardAmount
                val surpassStr = String.format(
                    context.getString(R.string.text_daily_rank_self_desc),
                    self.ppvSurpass
                )
                tvSelfSurpass.text = textSpanValue(
                    context,
                    surpassStr,
                    self.ppvSurpass.toString(),
                    com.iandroid.allclass.lib_common.R.color.color_ffb700,
                    true
                )
            }
        }
    }

    private fun getItemData(): DailyRankEntity? = data?.castObject<DailyRankEntity>()
}