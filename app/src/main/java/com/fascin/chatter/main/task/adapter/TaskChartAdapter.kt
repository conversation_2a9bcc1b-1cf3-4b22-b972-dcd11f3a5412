package com.fascin.chatter.main.task.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskDetailDataEntity

/**
 *  @author: LXL
 *  @description: 折线图、条形图
 *  @date: 2023/9/6 13:59
 */
class TaskChartAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    // 定义不同类型的Item类型标识
    private val VIEW_LINE_CHART = 0   //折线图
    private val VIEW_BAR_CHART = 1   //条形图

    private val dataList = mutableListOf<TaskDetailDataEntity>()

    fun updateData(data: MutableList<TaskDetailDataEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    // 创建ViewHolder并指定Item类型
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_LINE_CHART -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.itemview_line_chart, parent, false)
                LineChartViewHolder(view)
            }

            VIEW_BAR_CHART -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.itemview_bar_chart, parent, false)
                BarChartViewHolder(view)
            }

            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.itemview_bar_chart, parent, false)
                BarChartViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val itemData = dataList[position]
        when (holder) {
            is LineChartViewHolder -> {
                holder.bind(itemData)
            }

            is BarChartViewHolder -> {
                holder.bind(itemData)
            }
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    // 根据位置返回Item的类型
    override fun getItemViewType(position: Int): Int {
        val item = dataList[position]
        return VIEW_LINE_CHART
//        return when (item.ui_type) {
//            VIEW_LINE_CHART -> VIEW_LINE_CHART
//            VIEW_BAR_CHART -> VIEW_BAR_CHART
//            else -> VIEW_BAR_CHART
//        }
    }

    /**
     * 折线图
     */
    inner class LineChartViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun bind(entity: TaskDetailDataEntity) {
            itemView.run {

            }
        }
    }

    /**
     * 条形图
     */
    inner class BarChartViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        fun bind(entity: TaskDetailDataEntity) {
            itemView.run {

            }
        }
    }
}