package com.fascin.chatter.main.match.view

import android.app.Activity
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.inputmethod.EditorInfo
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.beans.ChatAtInfo
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardDetector
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardObserver
import com.iandroid.allclass.lib_common.utils.keyboard.KeyboardUtils
import com.iandroid.allclass.lib_common.views.input.MultiInputView
import kotlinx.android.synthetic.main.activity_bonus_match_hub.view.multi_board_layout
import kotlinx.android.synthetic.main.activity_bonus_match_hub.view.multi_input_count
import kotlinx.android.synthetic.main.activity_bonus_match_hub.view.multi_input_edit
import kotlinx.android.synthetic.main.activity_bonus_match_hub.view.multi_input_send

/**
 * Created by david on 2021/4/15.
 */

class MomentInputViewImpl(override val itemView: View) : MultiInputView {

    private var callback: MultiInputView.Callback? = null
    private var limitMaxLength: Int = 200

    private var inputBoardHeight: Int = 0
    private var chatAtInfo: ChatAtInfo? = null
    private var draftText: String = ""

    private val keyboardDetector: KeyboardDetector = KeyboardDetector(itemView.context as Activity)

    init {
        initKeyboardDetector()
        initMultiInputViews()
    }

    override fun setCallback(callback: MultiInputView.Callback) {
        this.callback = callback
    }

    override fun popup() {
        itemView.postDelayed({
            itemView.multi_input_edit.requestFocus()
            callback?.onInputPop()
            KeyboardUtils.showKeyboard(itemView.multi_input_edit)
        }, 150L)
        layoutInputBoard(0)
        itemView.multi_input_edit.hint = AppContext.getString(R.string.bonus_flash_chat_hint)

        itemView.multi_input_edit.requestFocus()
        if (!draftText.isNullOrEmpty()) {
            itemView.multi_input_edit.setText(draftText)
            itemView.multi_input_edit.setSelection(draftText.length)
        }
    }

    override fun dismiss() {
        callback?.onInputHeightChanged(0)
        itemView.multi_input_edit.clearFocus()
        KeyboardUtils.hideKeyboard(itemView.multi_input_edit)
    }

    override fun clear() {
        itemView.multi_input_edit.setText("")
        chatAtInfo = null
        draftText = ""
    }

    override fun setOutputInterval(interval: Int) {
    }

    override fun setLimitMaxLength(maxLength: Int) {
        this.limitMaxLength = maxLength
    }

    override fun insertChatAtInfo(chatAtInfo: ChatAtInfo?) {
        if (chatAtInfo == null && this.chatAtInfo != null) {
            clear()
        }

        if (chatAtInfo == null || this.chatAtInfo?.equals(chatAtInfo) == true) {
            return
        }

        clear()
        this.chatAtInfo = chatAtInfo
    }

    private fun initKeyboardDetector() {
        keyboardDetector.setKeyboardHeightObserver(object : KeyboardObserver {
            private var keyboardHeight = 0
            override fun onKeyboardHeightChanged(height: Int, orientation: Int) {
                if (height != keyboardHeight) {
                    keyboardHeight = height
                    val height = if (keyboardHeight < 0) 0 else keyboardHeight
                    if (layoutInputBoard(height)) {
                        callback?.onInputHeightChanged(height)
                    }
                }
            }
        })
        itemView.post { keyboardDetector.start() }
    }

    private fun initMultiInputViews() {
        initInputEditText()
        itemView.multi_input_send.setOnClickListener { outputEditTextValue() }

        itemView.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewDetachedFromWindow(v: View) {
                keyboardDetector.close()
            }

            override fun onViewAttachedToWindow(v: View) {
            }
        })
        itemView.setOnClickListener { callback?.onInputDismiss() }
        itemView.setOnTouchListener { v, _ ->
            v.parent.requestDisallowInterceptTouchEvent(true)
            false
        }
    }

    private fun initInputEditText() {
        itemView.multi_input_edit.run {
            addTextChangedListener(object : TextWatcher {
                private var beforeText: String = ""
                private var afterText: String = ""
                override fun beforeTextChanged(
                    s: CharSequence?, start: Int, count: Int, after: Int
                ) {
                    beforeText = s.toString()
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                    afterText = s.toString()
                    draftText = itemView.multi_input_edit.text.toString()
                    AppModule.userActive()
                    val currentCount = afterText.length
                    itemView.multi_input_count.text = "$currentCount/200"
                    if (currentCount >= 30) {
                        itemView.multi_input_send.setImageResource(R.drawable.ic_chat_send)
                    } else {
                        itemView.multi_input_send.setImageResource(com.iandroid.allclass.lib_common.R.drawable.ic_chat_input_send_disable)
                    }
                }

                override fun afterTextChanged(p0: Editable?) {
                    if (p0 != null && p0.trim().isNullOrEmpty()) {
                        itemView.multi_input_edit.hint =
                            AppContext.getString(R.string.bonus_flash_chat_hint)
                    }
                }
            })

            setOnEditorActionListener { _, action, _ ->
                if (action == EditorInfo.IME_ACTION_SEND) {
                    outputEditTextValue().not()
                } else {
                    false
                }
            }
            setOnClickListener {
                turnInputMode()
            }
        }
    }

    private fun layoutInputBoard(height: Int): Boolean {
        if (inputBoardHeight == height) return false
        itemView.multi_board_layout.layoutParams.height = height
        itemView.multi_board_layout.requestLayout()
        inputBoardHeight = height
        itemView.multi_board_layout.show(height > 0)
        return true
    }


    private fun turnInputMode() {
        itemView.multi_input_edit.requestFocus()
        KeyboardUtils.showKeyboard(itemView.multi_input_edit)
    }

    private fun outputEditTextValue(): Boolean {
        val content = itemView.multi_input_edit.text.toString().trim()
        if (content.length < 30) {
            ToastUtils.showCenterToast(R.string.bonus_flash_chat_hint)
            return false
        }
        callback?.onOutputText(content, null, chatAtInfo)
        clear()
        return true
    }
}

