package com.fascin.chatter.main.task.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import com.fascin.chatter.R
import com.fascin.chatter.bean.RewardCardChildEntity
import com.iandroid.allclass.lib_basecore.utils.SpanUtil
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.view_week_task_progress.view.llDayView
import kotlinx.android.synthetic.main.view_week_task_progress.view.svDayView
import kotlinx.android.synthetic.main.view_week_task_progress.view.tvTitle
import kotlinx.android.synthetic.main.view_week_task_progress.view.tvWeekProgress
import kotlinx.android.synthetic.main.view_week_task_progress.view.weekProgress


/**
 * @Desc: Task任务周完成进度
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
class WeekTaskProgressView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    ConstraintLayout(context, attrs, defStyleAttr) {

    init {
        View.inflate(context, R.layout.view_week_task_progress, this)
    }

    /**
     * 设置数据
     * @param cardChild day任务、week任务
     */
    fun setViewData(cardChild: RewardCardChildEntity?) {
        cardChild?.let { child ->
            if (child.is_period == Values.taskTypeDay) {
                weekProgress.show(false)
                svDayView.show(true)
                addStatusView(child.careProgress?.list)
            } else if (child.is_period == Values.taskTypeWeek) {
                svDayView.show(false)
                weekProgress.show(true)
                child.careProgress?.let {
                    weekProgress.max = (it.total * 1000f).toInt()
                    weekProgress.progress = (it.progress * 1000f).toInt()
                }
            }
            setTitleText(child.must, child.name, child.task_uint)
            tvWeekProgress.text = child.task_progress
        }
    }

    /**
     * taskStatus  item: 0未开始/进行中 1完成 2未完成
     */
    private fun addStatusView(taskStatus: ArrayList<Int>?) {
        llDayView.removeAllViews()
        taskStatus?.forEach {
            llDayView.addView(ImageView(context).apply {
                layoutParams = LinearLayout.LayoutParams(24.toPx, 24.toPx).apply {
                    marginEnd = 8.toPx
                }
                setImageResource(
                    when (it) {
                        1 -> R.drawable.ic_revitalize_select
                        2 -> R.mipmap.ic_week_task_failed
                        else -> R.mipmap.ic_week_task_progressing
                    }
                )
            })
        }
    }

    private fun setTitleText(must: Int, desc: String, taskUnit: String) {
        SpanUtil.create().addForeColorSection(
            if (must == 1) "[Require] " else "[Optional] ",
            Color.parseColor(if (must == 1) "#F5222D" else "#262626")
        ).addForeColorSection("$desc: ", Color.parseColor("#262626"))
            .addForeColorSection(taskUnit, ContextCompat.getColor(context, com.iandroid.allclass.lib_basecore.R.color.cl_9370DB))
            .showIn(tvTitle)
    }
}