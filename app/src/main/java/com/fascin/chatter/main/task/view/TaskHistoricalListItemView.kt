package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.fascin.chatter.main.task.adapter.TaskHistoricalAdapter
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.itemview_task_historical_list.view.rvTaskHistorical

/**
 * @Desc: task模块task模块历史 item
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
@RvItem(id = AppViewType.taskHistoricalListItemView, spanCount = 1)
class TaskHistoricalListItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    private var contentAdapter: TaskHistoricalAdapter? = null

    override fun attachLayoutId(): Int {
        return R.layout.itemview_task_historical_list
    }

    override fun initView(context: Context?, view: View?) {
        itemView.rvTaskHistorical.layoutManager = LinearLayoutManager(context)
        contentAdapter = TaskHistoricalAdapter()
        itemView.rvTaskHistorical.adapter = contentAdapter
    }

    override fun setView() {
        val entity = getItemData() ?: return
        contentAdapter?.updateData(entity)
    }

    private fun getItemData(): ArrayList<TaskWeekTitleEntity>? = data?.castObject<ArrayList<TaskWeekTitleEntity>>()
}