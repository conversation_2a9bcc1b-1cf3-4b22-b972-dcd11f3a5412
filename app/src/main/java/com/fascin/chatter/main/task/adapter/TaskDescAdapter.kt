package com.fascin.chatter.main.task.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskTableItemDescEntity
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_task_desc.view.llRoot
import kotlinx.android.synthetic.main.itemview_task_desc.view.tvContent

/**
 * @Desc: 任务说明弹窗表格adapter
 * @Created: Quan
 * @Date: 2023/11/10
 */
class TaskDescAdapter(
    private val isMatchParent: Boolean,
    val spanCount: Int,
    val dataList: ArrayList<TaskTableItemDescEntity> = arrayListOf()
) : RecyclerView.Adapter<TaskDescAdapter.ViewHolder>() {

    private var maxWidth: Int = 0

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun setMaxWidth(parentWidth: Int) {
        maxWidth = parentWidth
        notifyDataSetChanged()
    }

    fun setData(datas: List<TaskTableItemDescEntity>) {
        dataList.clear()
        if (datas.isNotEmpty()) {
            dataList.addAll(datas)
        }
        notifyDataSetChanged()
    }

    fun getItem(position: Int): TaskTableItemDescEntity {
        return dataList[position]
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context).inflate(R.layout.itemview_task_desc, parent, false)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        setItemMeasure(holder)
        getItem(position).let {
            holder.itemView.llRoot.setBackgroundResource(
                if (it.type == TaskTableItemDescEntity.TYPE_GRAY) R.color.color_f0f0f0
                else R.color.white
            )

            holder.itemView.tvContent?.text = it.content
        }
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    private fun setItemMeasure(holder: ViewHolder) {
        holder.itemView.run {
            llRoot.layoutParams.let {
                if (!isMatchParent) {
                    it.width = if (maxWidth / 104f.toPx.toInt() >= spanCount) {
                        maxWidth / spanCount
                    } else {
                        104f.toPx.toInt()
                    }
                } else {
                    it.width = ViewGroup.LayoutParams.MATCH_PARENT
                }
                holder.itemView.llRoot.layoutParams = it
            }
        }
    }
}
