package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import kotlinx.android.synthetic.main.itemview_task_historical_place.view.ShimmerLayout

/**
 * @Desc: task模块task模块历史 item
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
@RvItem(id = AppViewType.taskHistoricalPlaceItemView, spanCount = 1)
class TaskHistoricalPlaceItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun attachLayoutId(): Int {
        return R.layout.itemview_task_historical_place
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun setView() {
        itemView?.run {
            ShimmerLayout.startShimmerAnimation()
        }
    }

}