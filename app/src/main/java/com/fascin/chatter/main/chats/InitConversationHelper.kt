package com.fascin.chatter.main.chats

import com.fascin.chatter.bean.event.ConversationRefreshEvent
import com.fascin.chatter.im.IMModule
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.beans.ConversationEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.conversationlist.model.SingleConversation
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import java.util.Collections
import java.util.concurrent.CopyOnWriteArrayList

/**
 * @Desc:从服务端获取最近的会话列表数据，和融云本地会话列表合并展示，解决融云本地会话列表数据不全的问题。
 *      （卸载、清数据后在登陆，加载较慢的过程中会导致会话列表显示异常）
 * @Created: Quan
 * @Date: 2024/11/11
 */
object InitConversationHelper {

    // 从本地融云数据库取数据中靠前的最大条数
    private var maxRongCount = 500;
    private var isInit = false
    private var requesting = false
    private var initConversions = Collections.synchronizedList(ArrayList<ConversationEntity>())

    /**
     * 通过自己服务器获取的最近会话列表数据
     */
    fun requestInitConversations(callback: (Boolean) -> Unit) {
        if (!isInit && !requesting) {
            requesting = true
            IMModule.instance.getInitConversations { isSuccess, list ->
                requesting = false
                if (isSuccess) {
                    isInit = true
                    initConversions.clear()
                    initConversions.addAll(list)
                    callback.invoke(true)
                } else {
                    isInit = false
                    callback.invoke(false)
                }
            }
        }
    }

    /**
     * 合并融云本地会话列表和服务端获取的最近会话列表
     */
    fun mergerConversations(conversations: List<BaseUiConversation>): List<BaseUiConversation> {
        // 取最多500条数据
        val limitedList = conversations?.take(maxRongCount) ?: emptyList()
        val newConversations = CopyOnWriteArrayList<BaseUiConversation>().apply {
            if (conversations.isNotEmpty()) {
                addAll(limitedList)
            }
        }
        if (isInit && initConversions.isNotEmpty()) {
            initConversions.forEach { con ->
                // conversations没有时，创建一个
                if (!limitedList.any { it.mCore.targetId == con.targetId }) {
                    newConversations.add(
                        SingleConversation(AppContext.context, Conversation().apply {
                            targetId = con.targetId
                            conversationTitle = con.userName
                            portraitUrl = con.portraitUrl
                            unreadMessageCount = 0
                            conversationType = Conversation.ConversationType.PRIVATE
                            sentTime = con.chatTime.times(1000L)
                            latestMessageDirection = when (con.msgFlag) {
                                ChatsConfig.FlagReceived, ChatsConfig.FlagUnlocked -> Message.MessageDirection.RECEIVE
                                else -> Message.MessageDirection.SEND
                            }
                            // 添加一个自定义的objectName,代表是从后台获取的会话
                            objectName = "initConversation"
                            channelId = ""
                        })
                    )
                }
            }
        }
        return newConversations
    }

    /**
     * 删除会话
     */
    fun removeConversion(targetId: String) {
        initConversions?.forEachIndexed { index, conversationEntity ->
            if (conversationEntity.targetId == targetId) {
                initConversions.removeAt(index)
                SimpleRxBus.post(ConversationRefreshEvent())
                return
            }
        }
    }
}