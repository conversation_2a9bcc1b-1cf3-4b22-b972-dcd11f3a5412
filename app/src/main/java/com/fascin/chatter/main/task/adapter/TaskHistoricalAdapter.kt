package com.fascin.chatter.main.task.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskIntent
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import kotlinx.android.synthetic.main.itemview_task_historical_child.view.tvTaskDate
import kotlinx.android.synthetic.main.itemview_task_historical_child.view.tvTaskStatus
import kotlinx.android.synthetic.main.itemview_task_historical_child.view.tvTaskTitle

/**
 * @Desc: task模块Historical task child adapter
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
class TaskHistoricalAdapter : RecyclerView.Adapter<TaskHistoricalAdapter.ViewHolder>() {

    private val dataList = mutableListOf<TaskWeekTitleEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<TaskWeekTitleEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_task_historical_child, parent, false)
        )
    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            with(item) {
                tvTaskTitle.text = task_title
                tvTaskDate.text = buildString {
                    append("Check ")
                    append(getDateStr(startTime, endTime))
                }
                tvTaskStatus.show(
                    status != TaskWeekTitleEntity.STATUS_IN_PROGRESS
                )
                tvTaskStatus.text = ""
                when (status) {
                    TaskWeekTitleEntity.STATUS_IN_PROGRESS -> {
                        // 进行中
                        tvTaskStatus.show(false)
                    }

                    TaskWeekTitleEntity.STATUS_ACHIEVED -> {
                        // 完成
                        tvTaskStatus.setBackgroundResource(R.drawable.bg_def1da_r99)
                        tvTaskStatus.textColorResource = com.iandroid.allclass.lib_common.R.color.color_24C004
                        tvTaskStatus.text = statusDesc.ifEmpty {
                            context.getString(R.string.task_historical_status_achieved)
                        }
                    }

                    TaskWeekTitleEntity.STATUS_TERMINATED -> {
                        // 终止
                        tvTaskStatus.setBackgroundResource(R.drawable.bg_f0f0f0_r999)
                        tvTaskStatus.textColorResource = com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
                        tvTaskStatus.text = statusDesc.ifEmpty {
                            context.getString(R.string.task_historical_status_terminated)
                        }
                    }

                    else -> {
                        // 1成功，5终止， 0进行中，其他失败
                        tvTaskStatus.setBackgroundResource(R.drawable.bg_fa4740_20_r99)
                        tvTaskStatus.textColorResource = R.color.color_ff4d4f
                        tvTaskStatus.text = statusDesc.ifEmpty {
                            context.getString(R.string.task_historical_status_failed)
                        }
                    }
                }
                // item点击事件
                clickWithTrigger {
                    context.routeAction(ActionEntity().apply {
                        id = ActionType.actionTaskHistoricalDetail
                        param = TaskIntent().apply {
                            taskId = item.id
                            isHistory = true
                        }
                    })
                }
            }
        }
    }

    private fun getDateStr(startTime: Long, endTime: Long): String {
        val start = DateUtils.formatDate(startTime * 1000, "yyyy.MM.dd")
        val end = DateUtils.formatDate(endTime * 1000, "MM.dd")
        return "$start-$end"
    }
}