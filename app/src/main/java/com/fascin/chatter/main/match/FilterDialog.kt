package com.fascin.chatter.main.match

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventKey
import kotlinx.android.synthetic.main.dialog_matched_filter.FilteChMember
import kotlinx.android.synthetic.main.dialog_matched_filter.FilteChOnline

class FilterDialog(
    var config: Int,
    private val completeBlock: (Int) -> Unit
) : BaseDialogFragment() {

    override fun onStart() {
        super.onStart()
        setTopPopupAttr(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_matched_filter, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        AppModule.userActive()
        FilteChMember.isChecked = (config and FlagOpenMember) > 0
        FilteChMember.setOnClickListener {
            completeBlock.invoke(config xor (FlagOpenMember))
            AppRepository.eventTrace(EventKey.K_match_fliter_member)
            dismissAllowingStateLoss()
        }

        FilteChOnline.isChecked = (config and FlagOpenOnline) > 0
        FilteChOnline.setOnClickListener {
            completeBlock.invoke(config xor (FlagOpenOnline))
            AppRepository.eventTrace(EventKey.K_match_fliter_online)
            dismissAllowingStateLoss()
        }
    }

    companion object {
        const val FlagOpenMember = 0x01
        const val FlagOpenOnline = 0x02
    }
}