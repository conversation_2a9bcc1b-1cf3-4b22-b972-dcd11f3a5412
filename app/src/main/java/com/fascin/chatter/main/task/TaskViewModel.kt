package com.fascin.chatter.main.task

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.bean.ActivitiesEntity
import com.fascin.chatter.bean.DailyRankEntity
import com.fascin.chatter.bean.DailyRankRuleEntity
import com.fascin.chatter.bean.RankEntity
import com.fascin.chatter.bean.TaskCatalogEntity
import com.fascin.chatter.bean.TaskEntity
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel

/**
 * @Desc: task模块
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
class TaskViewModel : BaseViewModel() {

    val taskInfoLiveData = MutableLiveData<TaskEntity>()
    val taskInfoErrorLiveData = MutableLiveData<String>()

    val taskCatalogLiveData = MutableLiveData<List<TaskCatalogEntity>>()
    val taskCatalogErrorLiveData = MutableLiveData<Any>()

    val taskHistoricalListLiveData = MutableLiveData<List<TaskWeekTitleEntity>>()
    val taskHistoricalListErrorLiveData = MutableLiveData<Any>()

    val activitiesListLiveData = MutableLiveData<List<ActivitiesEntity>>()

    val rankDataLiveData = MutableLiveData<DailyRankEntity>()
    val rankDataError = MutableLiveData<String>()

    /**
     * 获取task模块数据
     * @param taskID 任务id 不传或者为0，表示获取当前进行中的任务，会返回历史任务列表。其他非0表示查询具体历史任务
     */
    fun getTaskInfo(taskID: Int = 0) {
        compositeDisposable?.add(
            AppRepository.getTasksInfo(taskID)
                .subscribe({
                    taskInfoLiveData.postValue(it)
                }, {
                    taskInfoErrorLiveData.postValue(it.message)
                })
        )
    }

    fun getTaskCatalogInfo() {
        compositeDisposable?.add(
            AppRepository.getTaskCatalogInfo()
                .subscribe({
                    taskCatalogLiveData.postValue(it)
                }, {
                    taskCatalogErrorLiveData.postValue(it.message)
                })
        )
    }

    fun getTaskHistoricalList() {
        compositeDisposable?.add(
            AppRepository.getTaskHistoryList()
                .subscribe({
                    taskHistoricalListLiveData.postValue(it)
                }, {
                    taskHistoricalListErrorLiveData.postValue(it.message)
                })
        )
    }

    fun getActivitiesList() {
        compositeDisposable?.add(
            AppRepository.getActivitiesList()
                .subscribe({
                    activitiesListLiveData.postValue(it)
                }, {
                })
        )
    }

    /**
     * 获取排行榜数据
     * @param selectType 1-新人榜，0-W2榜
     */
    fun getRankData(selectType: Int) {
        compositeDisposable?.add(
            AppRepository.getRankList(selectType)
                .subscribe({
                    rankDataLiveData.postValue(it)
                }, {
                    rankDataError.postValue(it.message)
                })
        )
    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return TaskViewModel() as T
        }
    }
}