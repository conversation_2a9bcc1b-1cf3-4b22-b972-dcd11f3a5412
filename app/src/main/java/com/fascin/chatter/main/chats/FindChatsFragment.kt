package com.fascin.chatter.main.chats

import android.view.View
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.event.UIFindChatInitDataEvent
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.MixListFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import okhttp3.internal.filterList

/**
 * @Desc: 有会话记录的用户列表
 * @Created: Quan
 * @Date: 2023/12/13
 */
class FindChatsFragment : MixListFragment() {

    // 当前fragment的tabId
    private var flag: Int = 0

    private var isVisible: Boolean = false
    private var tagSortById: Int = ChatsConfig.TAG_NO_SEL

    override fun initView(view: View?) {
        super.initView(view)
        if (mixPageEntity == null) return
        flag = mixPageEntity?.tabId!!
        setRv()
        setPreView()
    }

    private fun setRv() {
        // 此页面数据一次性加载完成，无需刷新与加载更多
        recyclerViewSupport?.setCanPullDown(true)
        recyclerViewSupport?.setCanPullUp(false)
    }

    override fun fetchPageData(refresh: Boolean) {

    }

    override fun requestData(refresh: Boolean) {
        // 请求初始化数据
        SimpleRxBus.post(UIFindChatInitDataEvent())
    }

    fun setPreView() {
        setData(ArrayList<BaseRvItemInfo?>().also { list ->
            repeat(7) {
                list.add(BaseRvItemInfo(Any(), AppViewType.findChatsPlaceItemView, this))
            }
        })
    }

    private fun setData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) recyclerViewSupport?.updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    override fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.content = getString(R.string.page_nodata_find_chat)
            it.icRes = R.mipmap.ic_msg_setting_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            }, true)
        }
    }

    override fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        }, true)
    }

    /**
     * 数据发生变化，刷新页面
     */
    fun updatePageData(
        chatList: ArrayList<ExploreUserEntity>?,
        isError: Boolean = false
    ) {
        onRefreshComplete()
        if (isError) {
            addErrorView()
        } else {
            // 刷新数据时，回到顶部
            if (recyclerViewSupport?.infos?.isNotEmpty() == true)
                recyclerViewSupport?.scrollToTop()
            when (flag) {
                TabConfig.findChatsTabAgo -> {
                    // 7天前
                    notifyDataChange(chatList, ExploreUserEntity.TYPE_AGO)
                }

                TabConfig.findChatsTabAgoTwo -> {
                    // 3～7天前
                    notifyDataChange(chatList, ExploreUserEntity.TYPE_AGO_TWO)
                }

                TabConfig.findChatsTabWithin -> {
                    // 3天内
                    notifyDataChange(chatList, ExploreUserEntity.TYPE_WITHIN)
                }
            }
        }
    }

    fun tagSortChange(selTagId: Int) {
        this.tagSortById = selTagId
    }

    fun notifyItem(targetId: String) {
        recyclerViewSupport?.infos?.forEachIndexed { index, itemInfo ->
            if (itemInfo.data is ExploreUserEntity) {
                (itemInfo.data as ExploreUserEntity).also { item ->
                    if (item.imId == targetId) {
                        recyclerViewSupport?.updateViewByPosition(index)
                        return
                    }
                }
            }
        }
    }

    /**
     * 设置列表数据
     */
    private fun notifyDataChange(chatList: ArrayList<ExploreUserEntity>?, type: Int) {
        chatList?.filterList { withinDays == type }.also { list ->
            if (list?.isNotEmpty() == true)
                sortData(list as ArrayList<ExploreUserEntity>)
            setData(
                ArrayList<BaseRvItemInfo?>().also {
                    list?.forEach { entity ->
                        it.add(BaseRvItemInfo(entity, AppViewType.findChatsItemView, this))
                    }
                }, true
            )
        }
    }

    /**
     * 排序
     */
    private fun sortData(chatList: ArrayList<ExploreUserEntity>?) {
        chatList?.sortWith(compareByDescending<ExploreUserEntity> {
            //  如果开启了按tag筛选，将对应tag的排在前面
            it.contactTag == tagSortById
        }.thenByDescending {
            // 先按在线排
            UserOnlineHelper.getOnline(it.imId)
        }.thenByDescending {
            // 再按活跃状态排
            UserOnlineHelper.isActive(it.imId, it.lastActiveTime)
        }.thenByDescending {
            // 按总解锁数排
            it.platformUnlockNum
        }.thenByDescending {
            // 按model解锁数排
            it.unlockNum
        }.thenByDescending {
            // 按最后聊天时间降序排
            it.lastChatTime
        })
    }

    override fun fragmentVisible(visible: Boolean) {
        isVisible = visible
    }
}