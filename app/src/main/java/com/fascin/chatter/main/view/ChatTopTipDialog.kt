package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_chat_top_tip.tvGot

/**
 * @Desc: 置顶限制提示
 * @Created: Quan
 * @Date: 2024/4/17
 */
class ChatTopTipDialog() : BaseDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_chat_top_tip, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.859).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        tvGot.clickWithTrigger {
            dismissAllowingStateLoss()
        }
    }

    companion object {
        fun showNotifyItem() {
            AppContext.getTopFragmentActivity()?.also {
                ChatTopTipDialog().show(
                    it.supportFragmentManager, ChatTopTipDialog::javaClass.name
                )
            }
        }
    }
}