package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.fascin.chatter.R
import com.fascin.chatter.bean.chat.UserImExtraEntity
import com.fascin.chatter.bean.event.UIContactTagChangeEvent
import com.fascin.chatter.main.chats.ChatsConfig
import com.fascin.chatter.main.chats.ChatsViewModel
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.rong.imkit.userinfo.RongUserInfoManager
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.ivTagHeartSel
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.ivTagHotSel
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.ivTagMoneySel
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.ivTagTurtleSel
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.llTagHeart
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.llTagHot
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.llTagMoney
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.llTagTurtle
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.tvModelName
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.tvRemove
import kotlinx.android.synthetic.main.dialog_contact_tag_sel.tvUserName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * @Desc: 设置会话标签的弹窗
 * @Created: Quan
 * @Date: 2024/6/5
 */
class ContactTagSelDialog() : BaseDialogFragment() {

    private var viewModel: ChatsViewModel? = null
    private var targetId: String = ""
    private var tagId: Int = 0
    private var userName = ""

    constructor(targetId: String, tagId: Int, userName: String) : this() {
        this.targetId = targetId
        this.tagId = tagId
        this.userName = userName
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_contact_tag_sel, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.859).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        viewModel = ViewModelProvider(this).get(ChatsViewModel::class.java)
        tvUserName.text = userName
        RongUserInfoManager.getInstance().getUserInfo(getModelId(targetId))?.let { modelInfo ->
            tvModelName.text = modelInfo.name
        }
        setSelectedUI()
        llTagMoney.clickWithTrigger {
            change(ChatsConfig.TAG_MONEY, "money")
        }

        llTagHot.clickWithTrigger {
            change(ChatsConfig.TAG_HOT, "fire")
        }

        llTagHeart.clickWithTrigger {
            change(ChatsConfig.TAG_HEART, "heart")
        }

        llTagTurtle.clickWithTrigger {
            change(ChatsConfig.TAG_TURTLE, "stingy")
        }

        tvRemove.clickWithTrigger {
            change(ChatsConfig.TAG_NONE, "remove")
        }
    }

    private fun change(reTagId: Int, tabName: String) {
        if (tagId != reTagId) {
            tagId = reTagId
            setSelectedUI()
            viewModel?.setContactTag(targetId, reTagId)
            val userInfo = RongUserInfoManager.getInstance().getUserInfo(targetId)?.also { userInfo ->
                userInfo.extra = userInfo.extra?.jsonToObj<UserImExtraEntity>().also { extra ->
                    if (extra != null) {
                        extra.contactTag = reTagId
                    }
                }.toJsonString()
            }
            traceTagChange(tagId, tabName)
            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
            SimpleRxBus.post(UIContactTagChangeEvent(targetId, reTagId))
            lifecycleScope.launch(Dispatchers.IO) {
                delay(200)
                withContext(Dispatchers.Main) {
                    dismissAllowingStateLoss()
                }
            }
        }
    }

    private fun setSelectedUI() {
        ivTagMoneySel.setImageResource(
            if (tagId == ChatsConfig.TAG_MONEY) {
                R.drawable.ic_contact_tag_selected
            } else {
                R.drawable.ic_contact_tag_unsel
            }
        )
        ivTagHotSel.setImageResource(
            if (tagId == ChatsConfig.TAG_HOT) {
                R.drawable.ic_contact_tag_selected
            } else {
                R.drawable.ic_contact_tag_unsel
            }
        )
        ivTagHeartSel.setImageResource(
            if (tagId == ChatsConfig.TAG_HEART) {
                R.drawable.ic_contact_tag_selected
            } else {
                R.drawable.ic_contact_tag_unsel
            }
        )
        ivTagTurtleSel.setImageResource(
            if (tagId == ChatsConfig.TAG_TURTLE) {
                R.drawable.ic_contact_tag_selected
            } else {
                R.drawable.ic_contact_tag_unsel
            }
        )
    }

    private fun getModelId(imUserId: String): String {
        var targetIdIndex = imUserId.indexOf("_")
        if (targetIdIndex > 0) {
            return imUserId.substring(0, targetIdIndex)
        }
        return ""
    }

    /**
     * 埋点
     * 点击筛选器Type中的4个tag记录一次，不包含点击重置
     */
    private fun traceTagChange(tagId: Int, tagName: String) {
        CommonRepository.eventTrace(EventKey.im_tag_popup_select) {
            "chatterID" to UserController.getUserId()
            "targetID" to targetId
            "tagId" to tagId
            "tagName" to tagName
        }
    }

}