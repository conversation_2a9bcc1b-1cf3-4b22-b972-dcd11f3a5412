package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.BindModelIntent
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_top_host.tvGot


/**
 * 成为顶级主播去选model的提示
 */
class TopHostDialog() : BaseDialogFragment() {

    private var num = 0

    constructor(num: Int) : this() {
        this.num = num
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_top_host, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.86).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = true
        tvGot.clickWithTrigger {
            if (num > 0) {
                context.routeAction(ActionType.actionSelectModel) {
                    it.param = BindModelIntent().apply {
                        maxNum = num
                        from = 1
                    }
                }
            }
            dismissAllowingStateLoss()
        }
    }
}