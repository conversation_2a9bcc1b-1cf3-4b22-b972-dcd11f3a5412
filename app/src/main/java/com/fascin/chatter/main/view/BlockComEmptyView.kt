package com.fascin.chatter.main.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.itemview_block_comempty.view.comEmptyActionBtnOne
import kotlinx.android.synthetic.main.itemview_block_comempty.view.comEmptyActionBtnTwo
import kotlinx.android.synthetic.main.itemview_block_comempty.view.comEmptyContent
import kotlinx.android.synthetic.main.itemview_block_comempty.view.comEmptyIcon
import kotlinx.android.synthetic.main.itemview_block_comempty.view.comEmptyTitle

/**
created by wangkm
on 2020/9/12.
 */
@RvItem(id = AppViewType.comEmptyView, spanCount = 1)
class BlockComEmptyView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun setView() {
        var entity = getItemData() ?: return
        itemView?.run {
            // 图标处理
            comEmptyIcon.setImageResource(if (entity.icRes > 0) entity.icRes else R.mipmap.ic_no_more)

            comEmptyTitle.text = entity.title
            comEmptyTitle.show(!entity.title.isNullOrEmpty())

            comEmptyContent.text = entity.content
            comEmptyContent.show(!entity.content.isNullOrEmpty())

            comEmptyActionBtnOne.text = entity.btnOneTitle
            comEmptyActionBtnOne.show(!entity.btnOneTitle.isNullOrEmpty())
            comEmptyActionBtnOne.clickWithTrigger {
                entity.setOnClick?.invoke(EmptyEntity.CLICK_ONE_BTN)
            }

            comEmptyActionBtnTwo.text = entity.btnTwoTitle
            comEmptyActionBtnTwo.show(!entity.btnTwoTitle.isNullOrEmpty())
            comEmptyActionBtnTwo.clickWithTrigger {
                entity.setOnClick?.invoke(EmptyEntity.CLICK_TWO_BTN)
            }
        }
    }

    private fun getItemData(): EmptyEntity? = data?.castObject<EmptyEntity>()

    override fun attachLayoutId(): Int {
        return R.layout.itemview_block_comempty
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun getItemOffsets(
        recyclerView: RecyclerView,
        view: View,
        outRect: Rect,
        position: Int
    ): Boolean {
        return true
    }

    private fun getAction(): IRvItemAction? {
        return info?.callBack?.castObject<IRvItemAction>()
    }
}