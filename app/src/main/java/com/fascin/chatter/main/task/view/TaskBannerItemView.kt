package com.fascin.chatter.main.task.view

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ActivitiesEntity
import com.fascin.chatter.main.task.adapter.BannerAdapter
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.WebIntent
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.zhpan.bannerview.BannerViewPager
import com.zhpan.bannerview.constants.IndicatorGravity
import com.zhpan.indicator.enums.IndicatorSlideMode
import com.zhpan.indicator.enums.IndicatorStyle

/**
 * @Desc: task模块: task模块banner活动入口
 * @Created: QuanZH
 * @Date: 2024/12/4
 */
@RvItem(id = AppViewType.taskBannerItemView, spanCount = 1)
class TaskBannerItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    private var bannerView: BannerViewPager<ActivitiesEntity>? = null

    override fun attachLayoutId(): Int {
        return R.layout.itemview_activities_banner
    }

    override fun initView(context: Context?, view: View?) {
        itemView?.let {
            val checkedWidth = 20.toPx
            val normalWidth: Int = 6.toPx
            bannerView = itemView.findViewById(R.id.bannerViewPager)
            bannerView?.apply {
                this.setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    .setIndicatorSliderGap(4.toPx)
                    .setIndicatorSlideMode(IndicatorSlideMode.SCALE)
                    .setIndicatorHeight(6.toPx)
                    .setIndicatorGravity(IndicatorGravity.END)
                    .setIndicatorSliderColor(
                        Color.parseColor("#4D000000"), Color.parseColor("#FFFFFF")
                    )
                    .setIndicatorSliderWidth(normalWidth, checkedWidth)
            }
        }
    }

    override fun setView() {
        val bannerData: List<ActivitiesEntity> = getItemData() ?: return
        bannerView?.apply {
            adapter = BannerAdapter()
            setInterval(5000)
            setScrollDuration(800)
            setRoundCorner(12.toPx)
            setOnPageClickListener { _, position ->
                val entity = bannerData[position]
                if (entity.url.isNotEmpty()) {
                    context.routeAction(ActionType.actionTypeToWebActivity) {
                        it.param = WebIntent().also { webIntent ->
                            webIntent.showTitle = true
                            webIntent.url = entity.url
                        }
                    }
                }
            }
        }?.create(bannerData)
    }

    override fun getItemOffsets(
        parent: RecyclerView,
        view: View?,
        outRect: Rect,
        position: Int
    ): Boolean {
        return false
    }

    private fun getItemData(): List<ActivitiesEntity>? = data?.castObject<List<ActivitiesEntity>>()

}