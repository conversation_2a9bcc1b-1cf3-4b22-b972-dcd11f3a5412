package com.fascin.chatter.main.match

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.BonusMatchHubEntity
import com.fascin.chatter.bean.ChatterMatchedEntity
import com.fascin.chatter.bean.SayHiErrorEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.base.BaseViewModel
import com.iandroid.allclass.lib_common.beans.UIEventNewMatchCountUpdate
import com.iandroid.allclass.lib_common.network.ErrorCodeCheckUtils
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils

/**
created by wangkm
on 2020/9/12.
 */
class MatchViewModel : BaseViewModel() {
    val chatterMatchedLiveData = MutableLiveData<ChatterMatchedEntity>()
    val chatterMatchedErrorLiveData = MutableLiveData<Boolean>()

    val sayHiSuccessLiveData = MutableLiveData<String>()
    val sayHiErrorLiveData = MutableLiveData<SayHiErrorEntity>()

    val readLiveData = MutableLiveData<Boolean>()

    val bonusMatchHubLiveData = MutableLiveData<BonusMatchHubEntity>()
    val bonusMatchHubErrorLiveData = MutableLiveData<String>()

    val bonusHubChooseLiveData = MutableLiveData<BonusMatchHubEntity>()
    val bonusHubChooseErrorLiveData = MutableLiveData<String>()

    /**
     * 获取match列表
     * @param noIce config有0x80，no_ice传0不返回未破冰会话，否则传1返回
     */
    fun getChatterMatchedList(online: Int, member: Int, lastId: Int, noIce: Int) {
        compositeDisposable?.add(
            AppRepository.getChatterMatchedList(online, member, lastId, noIce)
                .subscribe({
                    chatterMatchedLiveData.postValue(it)
                    // 只有第一页数据返回new_match_num
                    if (lastId <= 0)
                        SimpleRxBus.post(UIEventNewMatchCountUpdate(it.new_match_num))
                }, {
                    chatterMatchedErrorLiveData.postValue(true)
                })
        )
    }

    fun sayHi(userId: String) {
        compositeDisposable?.add(
            AppRepository.sayHi(userId)
                .subscribe({
                    sayHiSuccessLiveData.postValue(userId)
                }, {
                    sayHiErrorLiveData.postValue(
                        SayHiErrorEntity().also { entry ->
                            entry.userId = userId
                            entry.desc = ErrorCodeCheckUtils.getError(it)
                        }
                    )
                })
        )
    }

    fun readNewMatch(toGetMatchList: Boolean = false) {
        if (AppModule.isMatchTabShow) {
            compositeDisposable?.add(
                AppRepository.readNewMatch()
                    .subscribe({
                        readLiveData.postValue(toGetMatchList)
                    }, {
                        readLiveData.postValue(toGetMatchList)
                    })
            )
        }
    }

    /**
     * 定向建联列表
     */
    fun bonusMatchHubList() {
        compositeDisposable?.add(
            AppRepository.bonusMatchHubList()
                .subscribe({
                    bonusMatchHubLiveData.postValue(it)
                }, {
                    bonusMatchHubErrorLiveData.postValue(ErrorCodeCheckUtils.getError(it))
                })
        )
    }

    /**
     * 定向建联 Like、Flash Chat操作
     */
    fun bonusMatchHubChoose(userId: String, mid: String, chooseType: Int, msg: String = "") {
        compositeDisposable?.add(
            AppRepository.bonusMatchHubChoose(userId, mid, chooseType, msg)
                .subscribe({
                    bonusHubChooseLiveData.postValue(it)
                    if (chooseType == 3) {
                        ToastUtils.showToast(R.string.sent_successfully)
                    }
                }, {
                    bonusHubChooseErrorLiveData.postValue(ErrorCodeCheckUtils.getError(it))
                })
        )

    }

    class ViewModeFactory : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            return MatchViewModel() as T
        }
    }
}