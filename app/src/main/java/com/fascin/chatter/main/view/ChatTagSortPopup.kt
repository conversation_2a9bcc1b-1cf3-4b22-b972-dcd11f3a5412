package com.fascin.chatter.main.view

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.PopupWindow
import com.fascin.chatter.R
import com.fascin.chatter.main.chats.ChatsConfig
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.utils.exts.toPx

/**
 * @Desc:
 * @Created: Quan
 * @Date: 2024/9/3
 */
object ChatTagSortPopup {

    fun showPopupMenu(context: Context, anchor: View, curTagId: Int, block: (Int) -> Unit) {
        val contentView = LayoutInflater.from(context).inflate(R.layout.popup_contact_tag_sel, null)

        val clSelMoney = contentView.findViewById<View>(R.id.clMoney)
        val clSelHot = contentView.findViewById<View>(R.id.clHot)
        val clSelHeart = contentView.findViewById<View>(R.id.clHeart)
        val clSelTurtle = contentView.findViewById<View>(R.id.clTurtle)

        val ivSelMoney = contentView.findViewById<ImageView>(R.id.ivSelMoney)
        val ivSelHot = contentView.findViewById<ImageView>(R.id.ivSelHot)
        val ivSelHeart = contentView.findViewById<ImageView>(R.id.ivSelHeart)
        val ivSelTurtle = contentView.findViewById<ImageView>(R.id.ivSelTurtle)

        when (curTagId) {
            ChatsConfig.TAG_MONEY -> {
                clSelMoney.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelMoney.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }

            ChatsConfig.TAG_HOT -> {
                clSelHot.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelHot.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }

            ChatsConfig.TAG_HEART -> {
                clSelHeart.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelHeart.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }

            ChatsConfig.TAG_TURTLE -> {
                clSelTurtle.setBackgroundResource(com.iandroid.allclass.lib_common.R.color.color_FDA946_a10)
                ivSelTurtle.setImageResource(R.drawable.ic_contact_tag_manu_sel)
            }
        }

        // 创建 PopupWindow
        val popupWindow = PopupWindow(
            contentView,
            200.toPx,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true
        )
        contentView.findViewById<View>(R.id.clMoney).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_MONEY) {
                block.invoke(ChatsConfig.TAG_MONEY)
                traceTagChange(ChatsConfig.TAG_MONEY, "money")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.clHot).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_HOT) {
                block.invoke(ChatsConfig.TAG_HOT)
                traceTagChange(ChatsConfig.TAG_HOT, "fire")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.clHeart).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_HEART) {
                block.invoke(ChatsConfig.TAG_HEART)
                traceTagChange(ChatsConfig.TAG_HEART, "heart")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.clTurtle).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_TURTLE) {
                block.invoke(ChatsConfig.TAG_TURTLE)
                traceTagChange(ChatsConfig.TAG_TURTLE, "stingy")
            }
            popupWindow.dismiss()
        }

        contentView.findViewById<View>(R.id.tvReset).setOnClickListener {
            if (curTagId != ChatsConfig.TAG_NO_SEL) {
                block.invoke(ChatsConfig.TAG_NO_SEL)
                traceTagChange(ChatsConfig.TAG_NO_SEL, "remove")
            }
            popupWindow.dismiss()
        }

        // 显示 PopupWindow 在 anchor 视图的下方
        popupWindow.showAsDropDown(anchor)
    }

    /**
     * 埋点
     * 点击筛选器Type中的4个tag记录一次，不包含点击重置
     */
    private fun traceTagChange(tagId: Int, tagName: String) {
        CommonRepository.eventTrace(EventKey.im_chat_type_sort) {
            "chatterID" to UserController.getUserId()
            "tagId" to tagId
            "tagName" to tagName
            "from" to 0
        }
    }
}