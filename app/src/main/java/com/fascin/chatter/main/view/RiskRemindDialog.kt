package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.RiskRemindEntity
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_risk_remind.tvGot
import kotlinx.android.synthetic.main.dialog_risk_remind.tvRemindContent

/**
 * 被风控的提示弹窗
 * 在新设备登陆旧账号时，展示弹窗
 */
class RiskRemindDialog() : BaseDialogFragment() {

    private var riskRemindEntity: RiskRemindEntity? = null

    constructor(riskRemindEntity: RiskRemindEntity) : this() {
        this.riskRemindEntity = riskRemindEntity
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_risk_remind, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.86).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false
        tvRemindContent.text = buildString {
            append(getString(R.string.risk_remind_content))
            riskRemindEntity?.social?.forEach { remind ->
                append("\n")
                append(remind.key)
                append(": ")
                append(remind.value)
            }
        }

        tvGot.clickWithTrigger {
            dismissAllowingStateLoss()
        }
    }
}