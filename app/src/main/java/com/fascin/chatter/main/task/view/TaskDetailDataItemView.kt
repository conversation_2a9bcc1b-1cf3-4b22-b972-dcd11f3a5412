package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskDetailDataEntity
import com.fascin.chatter.main.task.adapter.TaskChartAdapter
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.exts.castObject
import kotlinx.android.synthetic.main.itemview_task_detail_data.view.rvTaskChart

/**
 * @Desc: task模块 图表item
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
@RvItem(id = AppViewType.taskDetailDataItemView, spanCount = 1)
class TaskDetailDataItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {
    private var taskChartAdapter: TaskChartAdapter? = null

    override fun attachLayoutId(): Int {
        return R.layout.itemview_task_detail_data
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            rvTaskChart.isNestedScrollingEnabled = false  //禁用滑动
            rvTaskChart.layoutManager = LinearLayoutManager(context)
            taskChartAdapter = TaskChartAdapter()
            rvTaskChart.adapter = taskChartAdapter
            taskChartAdapter?.updateData(entity)
        }
    }

    private fun getItemData(): ArrayList<TaskDetailDataEntity>? = data?.castObject<ArrayList<TaskDetailDataEntity>>()

    override fun initView(context: Context?, view: View?) {
    }
}