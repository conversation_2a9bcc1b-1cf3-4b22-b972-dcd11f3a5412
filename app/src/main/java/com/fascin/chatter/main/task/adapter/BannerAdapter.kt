package com.fascin.chatter.main.task.adapter

import androidx.appcompat.widget.AppCompatImageView
import com.fascin.chatter.R
import com.fascin.chatter.bean.ActivitiesEntity
import com.iandroid.allclass.lib_common.GlideLoader.loadImage
import com.zhpan.bannerview.BaseBannerAdapter
import com.zhpan.bannerview.BaseViewHolder

/**
 * Created by: Dawn
 * Date: 2024/11/20 21:15
 * Description:
 */
class BannerAdapter : BaseBannerAdapter<ActivitiesEntity>() {

    override fun bindData(
        holder: BaseViewHolder<ActivitiesEntity>,
        data: ActivitiesEntity?,
        position: Int,
        pageSize: Int
    ) {
        data?.let {
            holder.findViewById<AppCompatImageView>(R.id.iv_banner)
                .loadImage(holder.itemView.context, it.banner, roundedCorners = 0)
        }
    }

    override fun getLayoutId(viewType: Int): Int {
        return R.layout.item_banner
    }
}