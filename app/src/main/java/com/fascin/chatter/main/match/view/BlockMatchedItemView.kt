package com.fascin.chatter.main.match.view

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.MatchedUserInfoItem
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.chats.ChatsConfig
import com.fascin.chatter.main.match.IMatchRvItemAction
import com.fascin.chatter.utils.SUDateUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.GlideLoader.loadImageCircleCrop
import com.iandroid.allclass.lib_common.utils.DoubleUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.setTextColorEx
import com.iandroid.allclass.lib_common.utils.exts.show
import io.rong.imkit.utils.RouteUtils
import io.rong.imlib.model.Conversation
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.cmtMatchTag
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.ivVip
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.matchTime
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.pbLoading
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.timePlace
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.userGreetView
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.userHeadA
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.userHeadB
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.userNickName
import kotlinx.android.synthetic.main.itemview_block_matched_user.view.userOnline
import java.text.SimpleDateFormat
import java.util.Date

/**
created by wangkm
on 2020/9/12.
 */
@RvItem(id = AppViewType.chatterMatchedItemView, spanCount = 1)
class BlockMatchedItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            userHeadB.loadImageCircleCrop(context, entity.u_headimg)
            userHeadA.loadImageCircleCrop(context, entity.m_headimg)
            userNickName.text = entity.u_nickname
            userNickName.setTextColorEx(Color.parseColor("#000000"))
            userGreetView.show(entity.greeted == 0 && !entity.greeting)
            pbLoading.show(entity.greeting)
            cmtMatchTag.setChatFlag(if (entity.greeted == 0) ChatsConfig.FlagMatched else ChatsConfig.FlagDelivered)
            ivVip.setVipStatus(entity.vip)
            userOnline?.show(
                UserOnlineHelper.isOnline(entity.im_id)
                        || UserOnlineHelper.isActive(entity.im_id, entity.lastActiveTime)
            )
            // 设置在线状态图标
            if (UserOnlineHelper.isOnline(entity.im_id)) {
                userOnline.setImageResource(R.drawable.shape_user_online)
            } else {
                userOnline.setImageResource(R.drawable.shape_user_active)
            }
            if (entity.matched_time > 0) {
                matchTime.text = SUDateUtils.getTimeDifference(entity.lastChatTime.times(1000))
            }
            timePlace.show(entity.lastChatTime > 0 && matchTime.text.isNotEmpty(), true)
            matchTime.show(entity.lastChatTime > 0 && matchTime.text.isNotEmpty(), true)

            userGreetView.clickWithTrigger {
                if (!DoubleUtils.isFastDoubleClick() && entity.greeted == 0) {
                    entity.greeted = 1
                    entity.greeting = true
                    userGreetView.show(false)
                    pbLoading.show(true)
                    getAction()?.onSayHi(entity.im_id)
                }
            }

            this.clickWithTrigger {
                RouteUtils.routeToConversationActivity(
                    context, Conversation.ConversationType.PRIVATE, entity.im_id, false
                )
            }
        }
    }


    private fun getItemData(): MatchedUserInfoItem? = data?.castObject<MatchedUserInfoItem>()

    override fun attachLayoutId(): Int {
        return R.layout.itemview_block_matched_user
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun getItemOffsets(
        recyclerView: RecyclerView, view: View, outRect: Rect, position: Int
    ): Boolean {
        return false
    }

    private fun getAction(): IMatchRvItemAction? {
        return info?.callBack?.castObject<IMatchRvItemAction>()
    }

    private fun formatDate(timeMillis: Long, format: String): String {
        val sdf = SimpleDateFormat(format)
        return sdf.format(Date(timeMillis))
    }
}