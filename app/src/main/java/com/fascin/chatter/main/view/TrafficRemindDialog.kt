package com.fascin.chatter.main.view

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.MatchPolicyEntity
import com.fascin.chatter.bean.event.UIMatchTrafficStatusEvent
import com.fascin.chatter.main.profile.ProfileViewModel
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import kotlinx.android.synthetic.main.dialog_traffic_remind.tvTrafficCancel
import kotlinx.android.synthetic.main.dialog_traffic_remind.tvTrafficSure

/**
 *  @author: LXL
 *  @description: 流量开关到期提醒弹窗
 *  @date: 2024/3/14 10:10
 */
class TrafficRemindDialog() : BaseDialogFragment() {
    private var matchPolicyEntity = MatchPolicyEntity()

    private val viewModel by lazy {
        ViewModelProvider(
            this,
            ProfileViewModel.ViewModeFactory()
        )[ProfileViewModel::class.java]
    }

    constructor(matchPolicyEntity: MatchPolicyEntity) : this() {
        this.matchPolicyEntity = matchPolicyEntity
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_traffic_remind, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.859).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false
        tvTrafficSure.text = String.format(getString(R.string.traffic_remind_sure), "${matchPolicyEntity.time} mins")

        tvTrafficSure.clickWithTrigger {
            viewModel.matchPolicySet(1) {
                SimpleRxBus.post(UIMatchTrafficStatusEvent(it))
                dismissAllowingStateLoss()
            }
        }
        tvTrafficCancel.clickWithTrigger {
            dismissAllowingStateLoss()
        }
    }
}