package com.fascin.chatter.main.task

import android.util.SparseArray
import android.view.View
import androidx.core.util.forEach
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.ActivitiesEntity
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.TaskCatalogEntity
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.beans.UIEventTaskRefresh
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.statusBarHeightForImmersive
import com.iandroid.allclass.lib_common.utils.timer.CurCountDownTimer
import com.iandroid.allclass.lib_common.utils.timer.IActionListener
import kotlinx.android.synthetic.main.fragment_task.rl_statusBar
import kotlinx.android.synthetic.main.fragment_task_catalog.taskCatalogRv
import kotlinx.android.synthetic.main.layout_title_bar.titleBarRightIcon
import kotlinx.android.synthetic.main.layout_title_bar.titleBarRightTwoIcon
import kotlinx.android.synthetic.main.layout_title_bar.titleBarTitle


/**
 * @Desc: 任务目录页
 * @Created: Quan
 * @Date: 2023/11/8
 */
class TaskCatalogFragment : BaseUiFragment(), IActionListener, IRvItemAction {

    private var viewModel: TaskViewModel? = null
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var refreshing: Boolean = false
    private var catalogs: List<TaskCatalogEntity>? = null
    private var activities: List<ActivitiesEntity>? = null

    // 计时器容器
    private val countDownMap = SparseArray<CurCountDownTimer?>()

    override fun attachLayoutId(): Int {
        return R.layout.fragment_task_catalog
    }

    override fun initView(view: View?) {
        super.initView(view)
        initTitleBar()
        initRv()
        // 设置骨骼视图
        setPreView()
        setViewModel()
    }

    private fun initTitleBar() {
        rl_statusBar.statusBarHeightForImmersive(requireContext())
        titleBarTitle.text = getText(R.string.main_tab_task)
        titleBarRightIcon.show(true)
        titleBarRightIcon.setImageResource(R.mipmap.ic_task_historical)
        titleBarRightIcon.clickWithTrigger {
            context.routeAction(ActionType.actionTaskHistoricalList)
        }
        //TODO 关闭排行榜 by mask
        titleBarRightTwoIcon.show(false)
        titleBarRightTwoIcon.setImageResource(R.mipmap.ic_rank_in)
        titleBarRightTwoIcon.clickWithTrigger {
            context.routeAction(ActionType.actionDailyRank)
        }
    }

    private fun initRv() {
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, taskCatalogRv, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    if (!refreshing) fetchPageData(true)
                    else recyclerViewSupport?.onHeaderRefreshComplete()
                }

                override fun onFooterRefresh() {

                }
            })
        }
    }

    private fun setPreView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.taskCatalogPlaceItemView, this))
            it.add(BaseRvItemInfo(Any(), AppViewType.taskCatalogPlaceItemView, this))
            it.add(BaseRvItemInfo(Any(), AppViewType.taskCatalogPlaceItemView, this))
        })
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(TaskViewModel::class.java).apply {
            taskCatalogLiveData.observe(this@TaskCatalogFragment) {
                refreshing = false
                recyclerViewSupport?.onHeaderRefreshComplete()
                catalogs = it

                val isEmpty = catalogs.isNullOrEmpty() && activities.isNullOrEmpty()

                if (isEmpty) {
                    addEmptyView()
                } else {
                    updateTaskData()
                }
            }

            taskCatalogErrorLiveData.observe(this@TaskCatalogFragment) {
                recyclerViewSupport?.onHeaderRefreshComplete()
                addErrorView()
            }

            compositeDisposable?.add(
                SimpleRxBus.observe(UIEventTaskRefresh::class) {
                    fetchPageData(true)
                }
            )

            //活动banner
            activitiesListLiveData.observe(this@TaskCatalogFragment) {
                activities = it
                updateTaskData()
            }
        }
    }

    /**
     * 获取数据
     */
    override fun fetchPageData(refresh: Boolean) {
        refreshing = true
        viewModel?.getActivitiesList()
        viewModel?.getTaskCatalogInfo()
    }

    private fun updateTaskData() {
        updateData(ArrayList<BaseRvItemInfo>().also { list ->
            if (activities?.isNotEmpty() == true) {
                list.add(
                    BaseRvItemInfo(
                        activities,
                        AppViewType.taskBannerItemView,
                        this@TaskCatalogFragment
                    )
                )
            }
            var index = 0
            catalogs?.forEach { entity ->
                index++
                list.add(
                    BaseRvItemInfo(
                        entity.also {
                            it.index = index
                        },
                        AppViewType.taskCatalogItemView,
                        this@TaskCatalogFragment
                    )
                )
            }
        })
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo>?) {
        if (itemTemp?.isNotEmpty() == true) recyclerViewSupport?.updateData(itemTemp, true)
        else addEmptyView()
    }

    /**
     * 计时器
     */
    override fun getCountDownTimer(tag: Int): CurCountDownTimer? {
        var countDownTimer = countDownMap.get(tag)
        if (countDownTimer == null) {
            countDownTimer = CurCountDownTimer()
            countDownTimer.startCountDown()
            countDownMap.put(tag, countDownTimer)
        }
        return countDownTimer
    }

    /**
     * 关闭所有倒计时
     */
    private fun closeCountDownTimer() {
        countDownMap.forEach { _, value ->
            value?.cancelCountDownAndRemoveListener()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        closeCountDownTimer()
    }

    /**
     * 数据错误视图
     */
    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        })
    }

    /**
     * 空视图
     */
    private fun addEmptyView() {
        updateData(ArrayList<BaseRvItemInfo>().also { list ->
            list.add(BaseRvItemInfo(EmptyEntity().also {
                it.content = getString(R.string.task_weekly_empty_content)
                it.icRes = R.mipmap.ic_public_album_empty
            }, AppViewType.comEmptyView, this))
        })
    }

    override fun startRefresh() {
        super.startRefresh()
        fetchPageData(true)
    }

    override fun onRefresh() {
        fetchPageData(true)
    }
}