package com.fascin.chatter.main.task.view

import android.content.Context
import android.graphics.Rect
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskCatalogEntity
import com.fascin.chatter.bean.TaskIntent
import com.fascin.chatter.main.task.TaskDescDialog
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.bean.ActionEntity
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import com.iandroid.allclass.lib_common.utils.timer.IActionListener
import com.iandroid.allclass.lib_common.utils.timer.ICountDownListener
import com.iandroid.allclass.lib_common.utils.timer.TAG_RV_ITEM
import kotlinx.android.synthetic.main.itemview_catalog_task.view.llRoot
import kotlinx.android.synthetic.main.itemview_catalog_task.view.llTime
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvContent
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvIndex
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvLevel
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvPeriod
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvTag
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvTime
import kotlinx.android.synthetic.main.itemview_catalog_task.view.tvTitle

/**
 * @Desc: task模块:Task目录item
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
@RvItem(id = AppViewType.taskCatalogItemView, spanCount = 1)
class TaskCatalogItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent), ICountDownListener {
    override fun attachLayoutId(): Int {
        return R.layout.itemview_catalog_task
    }

    override fun initView(context: Context?, view: View?) {
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            setRootBg(llRoot, (entity.index - 1))
            tvIndex.text = buildString { append(entity.index) }
            tvTitle.text = entity.title
            tvContent.text = entity.content
            tvTag.text = entity.status
            tvTag.show(entity.status.isNotEmpty())
            tvLevel.text = String.format(
                context.getString(R.string.task_catalog_level),
                entity.currentLevel
            )
            tvPeriod.text = String.format(
                context.getString(R.string.task_catalog_period),
                getPeriodStr(entity.startTime, entity.endTime)
            )

            //TODO close 弹出表格弹窗 by mask
            /*tvLevel.clickWithTrigger {
                // 弹出表格弹窗
                TaskDescDialog(TaskDescDialog.TYPE_SCROLL, entity).show(
                    fragmentManager, TaskDescDialog::javaClass.name
                )
            }*/

            clickWithTrigger {
                context.routeAction(ActionEntity().apply {
                    id = ActionType.actionTaskHistoricalDetail
                    param = TaskIntent().apply {
                        taskId = entity.id
                        isHistory = false
                        rewardInfo = entity.rewardInfo
                    }
                })
            }
        }
    }

    /**
     * 计时器，每秒触发一次
     */
    override fun onCountDownTick(day: Long, hour: Long, minute: Long, second: Long) {
        itemView?.llTime?.show(true)
        itemView?.tvTime?.text = buildString {
            append(day)
            append("d:")
            append(hour)
            append("h:")
            append(minute)
            append("m:")
            append(second)
            append("s")
        }
    }

    /**
     * 完成计时
     */
    override fun onCountDownFinish() {
        itemView?.tvTime?.text = buildString { append("0d:0h:0m:0s") }
    }


    override fun onAttached() {
        super.onAttached()
        val entity = getItemData() ?: return
        if (getActionListener() != null && getPrivacyDeadTime(entity.endTime) > 0) {
            getActionListener()?.getCountDownTimer(TAG_RV_ITEM)?.register(
                this, getPrivacyDeadTime(entity.endTime)
            )
            // 设置倒计时预览，避免1s空白
            getActionListener()?.getCountDownTimer(TAG_RV_ITEM)?.showCountDownTimer(
                getPrivacyDeadTime(entity.endTime),
                this
            )
        } else {
            itemView?.llTime?.show(false)
        }
    }

    override fun onDetached() {
        super.onDetached()
        if (getActionListener() != null) {
            getActionListener()?.getCountDownTimer(TAG_RV_ITEM)?.unregister(this)
        }
    }

    private fun setRootBg(rootView: View, position: Int) {
        rootView.setBackgroundResource(
            when (position % 5) {
                0 -> com.iandroid.allclass.lib_common.R.color.color_e6e6fa
                1 -> com.iandroid.allclass.lib_common.R.color.color_f0ffff
                2 -> com.iandroid.allclass.lib_common.R.color.color_f0fff0
                3 -> com.iandroid.allclass.lib_common.R.color.color_ffe4e1
                else -> com.iandroid.allclass.lib_common.R.color.color_f5f5f5
            }
        )
    }

    /**
     * 计算剩余倒计时,返回毫秒值
     */
    private fun getPrivacyDeadTime(deadTime: Long): Long {
        val countdown = deadTime * 1000 - System.currentTimeMillis()
        return if (countdown > 0) countdown else 0
    }

    private fun getPeriodStr(startTime: Long, endTime: Long): String {
        val start = DateUtils.formatDate(startTime * 1000, "yyyy.MM.dd HH:mm")
        val end = DateUtils.formatDate(endTime * 1000, "MM.dd HH:mm")
        return "$start-$end"
    }

    override fun getItemOffsets(
        parent: RecyclerView,
        view: View?,
        outRect: Rect,
        position: Int
    ): Boolean {
        val rectMargin = 16f.toPx.toInt()
        if (position == 0)
            outRect.top = rectMargin
        outRect.left = rectMargin
        outRect.right = rectMargin
        outRect.bottom = rectMargin
        return true
    }

    private fun getItemData(): TaskCatalogEntity? = data?.castObject<TaskCatalogEntity>()

    private fun getActionListener(): IActionListener? = callBack?.castObject<IActionListener>()
}