package com.fascin.chatter.main.chats

import android.util.Log
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.repository.AppRepository
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.utils.SPUtils
import io.rong.imkit.RongIM
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imlib.RongIMClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.Collections
import kotlin.coroutines.resume

/**
 * @Desc: 错误显示未读数的情况上报
 * @Created: Quan
 * @Date: 2024/3/19
 */
object UnreadEventHelper {

    private var tracedList = Collections.synchronizedList(ArrayList<String>())

    /**
     * 将存在错误显示未读数的情况上报
     */
    suspend fun errorMsgCountEventTag(conversations: List<BaseUiConversation>?, modelList: List<ModelUserEntity>) {
        if (modelList.isEmpty() || !AppController.isOpenUnreadError()) return
        withContext(Dispatchers.IO) {
            flow<String> {
                conversations?.forEach { conversation ->
                    val targetId = conversation.conversationIdentifier.targetId.orEmpty()
                    val modelId = getModelId(conversation.conversationIdentifier.targetId.orEmpty())
                    // 这个会话不属于任何model时
                    if (modelList.none { it.userId == modelId }) {
                        if (conversation.mCore.unreadMessageCount > 0) {
                            emit(targetId)
                        } else {
                            emit(checkHasUnread(conversation))
                        }
                    }
                }
            }.flowOn(Dispatchers.IO).catch {
                Log.e("errorMsgCountEventTag", "errorMsgCountEventTag error")
            }.collect {
                if (it != "-100" && traced(it)) {
                    updateTracedList(it)
                    // 上报
                    AppRepository.eventTrace(EventKey.K_unread_error) {
                        "chatterId" to UserController.getUserId()
                        "targetId" to it
                    }
                }
            }
        }
    }

    /**
     * 检查指定会话是否有未读消息
     */
    private suspend fun checkHasUnread(conversation: BaseUiConversation): String = suspendCancellableCoroutine { con ->
        RongIM.getInstance().getUnreadCount(
            conversation.conversationIdentifier.type,
            conversation.conversationIdentifier.targetId.orEmpty(),
            object : RongIMClient.ResultCallback<Int>() {
                override fun onSuccess(count: Int) {
                    if (con.isActive) {
                        con.resume(
                            if (count > 0)
                                conversation.mCore.targetId
                            else "-100"
                        )
                    }
                }

                override fun onError(e: RongIMClient.ErrorCode?) {
                    if (con.isActive) {
                        con.resume("-100")
                    }
                }
            })
    }

    private fun getTracedList() {
        if (tracedList.isNotEmpty()) return
        val tracedStr = SPUtils.getString(AppContext.context, UserController.attachAccount("TracedList"), "")
        if (tracedStr.isNotEmpty()) {
            tracedList.clear()
            tracedList.addAll(tracedStr.split(",").toMutableList())
        }
    }

    private fun updateTracedList(targetId: String) {
        getTracedList()
        val list = tracedList.toMutableList()
        if (!tracedList.contains(targetId)) {
            list.add(targetId)
            SPUtils.put(AppContext.context, UserController.attachAccount("TracedList"), list.joinToString(","))
        }
    }

    /**
     * 是否上报过
     */
    private fun traced(targetId: String): Boolean {
        return tracedList.contains(targetId)
    }

    private fun getModelId(imUserId: String): String {
        val targetIdIndex = imUserId.indexOf("_")
        if (targetIdIndex > 0) {
            return imUserId.substring(0, targetIdIndex)
        }
        return ""
    }
}