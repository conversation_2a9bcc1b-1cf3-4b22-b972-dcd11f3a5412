package com.fascin.chatter.main.task.view

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskWeekTitleEntity
import com.fascin.chatter.bean.TaskWeeklyEntity
import com.fascin.chatter.main.task.adapter.CurTaskContentAdapter
import com.fascin.chatter.main.task.adapter.TaskRewardAdapter
import com.iandroid.allclass.lib_basecore.utils.DateUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RvItem
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import com.iandroid.allclass.lib_common.widgets.StartPagerSnapHelper
import com.iandroid.allclass.lib_common.widgets.TitleItemDecoration
import kotlinx.android.synthetic.main.itemview_weekly_task.view.rvRewardCard
import kotlinx.android.synthetic.main.itemview_weekly_task.view.rvWeeklyTask
import kotlinx.android.synthetic.main.itemview_weekly_task.view.tvTaskFailed
import kotlinx.android.synthetic.main.itemview_weekly_task.view.tvWeeklyTask
import kotlinx.android.synthetic.main.itemview_weekly_task.view.tvWeeklyTaskDate
import kotlinx.android.synthetic.main.itemview_weekly_task.view.tvWeeklyTaskEmpty

/**
 * @Desc: task模块Weekly task item
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
@RvItem(id = AppViewType.taskWeeklyItemView, spanCount = 1)
class TaskWeeklyItemView(context: Context, fm: FragmentManager, parent: ViewGroup) :
    BaseRvItemView(context, fm, parent) {

    private var rewardAdapter: TaskRewardAdapter? = null
    private var contentAdapter: CurTaskContentAdapter? = null
    private var itemTitleDecoration: TitleItemDecoration? = null

    override fun attachLayoutId(): Int {
        return R.layout.itemview_weekly_task
    }

    override fun initView(context: Context?, view: View?) {
        context?.let {
            itemView.rvRewardCard.layoutManager = LinearLayoutManager(it, LinearLayoutManager.HORIZONTAL, false)
            rewardAdapter = TaskRewardAdapter(it)
            itemView.rvRewardCard.adapter = rewardAdapter
            StartPagerSnapHelper().attachToRecyclerView(itemView.rvRewardCard)
            itemView.rvWeeklyTask.layoutManager = LinearLayoutManager(it)
            contentAdapter = CurTaskContentAdapter()
            itemView.rvWeeklyTask.adapter = contentAdapter
            itemTitleDecoration = TitleItemDecoration(it, false).apply {
                setDimensionInfo(32f, 16f, 16f, false, 0)
            }
            itemView.rvWeeklyTask.addItemDecoration(itemTitleDecoration!!)
        }
    }

    override fun setView() {
        val entity = getItemData() ?: return
        itemView?.run {
            entity.titleInfo?.let { titleEntity ->
                tvWeeklyTask.text = titleEntity.task_title
                tvWeeklyTaskDate.text = getDateStr(titleEntity.startTime, titleEntity.endTime)
                // 查看历史时显示
                tvTaskFailed.show(entity.isHistory)
                tvTaskFailed.text = ""
                when (entity.titleInfo?.status) {
                    TaskWeekTitleEntity.STATUS_IN_PROGRESS -> {
                        // 进行中
                        tvTaskFailed.show(false)
                    }

                    TaskWeekTitleEntity.STATUS_ACHIEVED -> {
                        // 完成
                        tvTaskFailed.setBackgroundResource(R.drawable.bg_def1da_r99)
                        tvTaskFailed.textColorResource = com.iandroid.allclass.lib_common.R.color.color_24C004
                        tvTaskFailed.text = entity.titleInfo?.statusDesc?.ifEmpty {
                            context.getString(R.string.task_historical_status_achieved)
                        }
                    }

                    TaskWeekTitleEntity.STATUS_TERMINATED -> {
                        // 终止
                        tvTaskFailed.setBackgroundResource(R.drawable.bg_f0f0f0_r999)
                        tvTaskFailed.textColorResource = com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
                        tvTaskFailed.text = entity.titleInfo?.statusDesc?.ifEmpty {
                            context.getString(R.string.task_historical_status_terminated)
                        }
                    }

                    else -> {
                        // 1成功，5终止， 0进行中，其他失败
                        tvTaskFailed.setBackgroundResource(R.drawable.bg_fa4740_20_r99)
                        tvTaskFailed.textColorResource = R.color.color_ff4d4f
                        tvTaskFailed.text = entity.titleInfo?.statusDesc?.ifEmpty {
                            context.getString(R.string.task_historical_status_failed)
                        }
                    }
                }
            }
            rvRewardCard?.show(!entity.isHistory && entity.settlements?.isNotEmpty() == true)
            // 按weight升序排序，定位到完成级别最高的卡片
            entity.settlements?.sortedBy { it.weight }?.let { list ->
                rewardAdapter?.updateData(list)
                list.findLast { it.achieved == 1 }?.let {
                    itemView.rvRewardCard.smoothScrollToPosition(list.lastIndexOf(it))
                }
            }
            entity.childEntitys?.also {
                itemView.rvWeeklyTask.show(it.isNotEmpty())
                itemView.tvWeeklyTaskEmpty.show(it.isEmpty())
                itemTitleDecoration?.setData(it)
                contentAdapter?.updateData(it)
            }
        }
    }

    private fun getDateStr(startTime: Long, endTime: Long): String {
        val start = DateUtils.formatDate(startTime * 1000, "yyyy.MM.dd HH:mm")
        val end = DateUtils.formatDate(endTime * 1000, "MM.dd HH:mm")
        return "$start-$end"
    }

    private fun getItemData(): TaskWeeklyEntity? = data?.castObject<TaskWeeklyEntity>()
}