package com.fascin.chatter.main.task

import android.os.Bundle
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskIntent
import com.iandroid.allclass.lib_common.base.ChatterBaseActivity
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.exts.getColorEx

/**
 * @Desc: 历史任务详情
 * @Created: QuanZH
 * @Date: 2023/9/6
 */
class TaskHistoricalDetailActivity : ChatterBaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_task_historical_detail)
        setTitle(R.string.task_detail_title)
        titleTv.setTextColor(getColorEx(R.color.black))
        parseJsonParams<TaskIntent>()?.also {
            if (it.isHistory) {
                setTitle(R.string.task_historical_title)
            }
            if (it.taskId <= 0) {
                ToastUtils.showToast("param error!")
                finish()
            }
        }
        if (savedInstanceState == null) {
            supportFragmentManager.beginTransaction()
                .add(R.id.flFragment, TaskFragment())
                .commit()
        }
    }
}