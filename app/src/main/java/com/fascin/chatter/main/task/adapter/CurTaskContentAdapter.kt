package com.fascin.chatter.main.task.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskChildEntity
import com.fascin.chatter.bean.TaskDetailDataEntity
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.iandroid.allclass.lib_common.beans.AlertBtnEntity
import com.iandroid.allclass.lib_common.beans.AlertEntity
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeActionByParam
import com.iandroid.allclass.lib_common.utils.chart.BarChartManager
import com.iandroid.allclass.lib_common.utils.chart.MPChartUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.toPx
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.flChart
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.llPpvNum
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.taskBarChart
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.taskLineChart
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.tvContent
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.tvPPNum
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.tvPVNum
import kotlinx.android.synthetic.main.itemview_cur_task_content.view.tvTitle

/**
 * @Desc: task模块Weekly task child adapter
 * @Created: QuanZH
 * @Date: 2023/9/5
 */
class CurTaskContentAdapter : RecyclerView.Adapter<CurTaskContentAdapter.ViewHolder>() {

    // 定义不同类型的Item类型标识
    private val VIEW_LINE_CHART = 0   //折线图
    private val VIEW_BAR_CHART = 1   //条形图
    private val VIEW_BAR_DOUBLE_CHART = 2  //双柱形图

    private val dataList = mutableListOf<TaskChildEntity>()

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    fun updateData(data: List<TaskChildEntity>?) {
        dataList.clear()
        if (!data.isNullOrEmpty()) {
            dataList.addAll(data)
        }
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.itemview_cur_task_content, parent, false)
        )
    }

    override fun getItemCount(): Int = dataList.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = dataList[position]
        holder.itemView.run {
            with(item) {
                tvTitle.text = name
                tvContent.text = desc
//                weekTaskProgress.setViewData(
//                    is_period,
//                    chartData,
//                    total
//                )
                flChart.show(item.chartData != null)
                when (item.ui_type) {
                    VIEW_LINE_CHART -> {
                        taskLineChart.show(false)
                        taskBarChart.show(true)
                        llPpvNum.show(true)
                        bindBarChart(taskBarChart, VIEW_BAR_CHART, item.chartData!!)

                        taskBarChart.layoutParams =
                            FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 220.toPx)
                    }

                    VIEW_BAR_CHART -> {
                        taskLineChart.show(false)
                        taskBarChart.show(true)
                        llPpvNum.show(false)
                        bindBarChart(taskBarChart, VIEW_BAR_CHART, item.chartData!!)
                        taskBarChart.layoutParams =
                            FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 220.toPx)

                    }

                    VIEW_BAR_DOUBLE_CHART -> {
                        taskLineChart.show(false)
                        taskBarChart.show(true)
                        llPpvNum.show(true)
                        setPPVNum(holder.itemView, item.chartData!!)
                        bindBarChart(taskBarChart, VIEW_BAR_DOUBLE_CHART, item.chartData!!)
                        taskBarChart.layoutParams =
                            FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 240.toPx)
                    }
                }
                // item点击事件
                clickWithTrigger {
                    context.routeActionByParam<AlertEntity>(ActionType.actionTypeAlertDialog) {
                        it.title = name
                        it.content = desc
                        it.show_close = 2
                        it.contentMaxHeight = 216.toPx
                        it.btn_list = ArrayList<AlertBtnEntity>().also { list ->
                            list.add(
                                AlertBtnEntity().apply {
                                    title = context.getString(R.string.btn_got_it)
                                }
                            )
                        }

                    }
                }
            }
        }
    }

    private fun bindLineChart(taskLineChart: LineChart, entity: List<TaskDetailDataEntity>) {
        val lineChartData = ArrayList<Entry>()

        entity.forEachIndexed { index, data ->
            lineChartData.add(Entry(index.toFloat(), data.chartInfo))
        }
        entity.maxOfOrNull { it.chartInfo }?.let {
            MPChartUtils.configLineChart(taskLineChart, it, entity.size)
            val lineChartDataSet: LineDataSet =
                MPChartUtils.getLineData(taskLineChart.context, lineChartData, "LineChart")
            MPChartUtils.initLineData(taskLineChart, LineData(lineChartDataSet))
        }
    }

    private fun bindBarChart(barChart: BarChart, uiType: Int, entity: List<TaskDetailDataEntity>?) {

        val barChartData = ArrayList<BarEntry>()
        entity?.let { uiData ->
            if (uiType == 2) {
                val barChartManager = BarChartManager(barChart, uiData.maxOf { maxOf(it.pp, it.pv) })
                val chartDataMap = LinkedHashMap<String, List<Float>>()
                val yValue1: MutableList<Float> = mutableListOf()
                val yValue2: MutableList<Float> = mutableListOf()
                uiData.forEach { data ->
                    yValue1.add(data.pp)
                    yValue2.add(data.pv)
                }
                chartDataMap["PP"] = yValue1
                chartDataMap["PV"] = yValue2
                barChartManager.showBarChart(MPChartUtils.xLabels.take(yValue1.size), chartDataMap)
            } else {
                uiData.forEachIndexed { index, data ->
                    barChartData.add(BarEntry(index.toFloat(), data.chartInfo))
                }
                uiData.maxOfOrNull { it.chartInfo }?.let { MPChartUtils.configBarChart(barChart, it, entity.size) }
                MPChartUtils.initBarChart(barChart, barChartData, "BarChart")
            }
        }
    }

    private fun setPPVNum(itemView: View, entity: List<TaskDetailDataEntity>?) {
        entity?.let { list ->
            itemView.tvPPNum.text = list.sumOf { it.pp.toInt() }.toString()
            itemView.tvPVNum.text = list.sumOf { it.pv.toInt() }.toString()
        }
    }
}
