package com.fascin.chatter.main.chats

import android.content.Context
import android.net.Uri
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.ActiveStatusEntity
import com.fascin.chatter.bean.BonusTipEntity
import com.fascin.chatter.bean.ChatterBindUpdateEntity
import com.fascin.chatter.bean.ChatterShiftEntity
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.PenaltyNotifyEntity
import com.fascin.chatter.bean.RevitalizeInChangeEntity
import com.fascin.chatter.bean.chat.PrivacyUnlockDBEntity
import com.fascin.chatter.bean.chat.UserImExtraEntity
import com.fascin.chatter.bean.event.ConversationRefreshEvent
import com.fascin.chatter.bean.event.UIAiChatChangeEvent
import com.fascin.chatter.bean.event.UIContactTagChangeEvent
import com.fascin.chatter.bean.event.UIEventUserBlock
import com.fascin.chatter.bean.event.UIFlashChatNumChangeEvent
import com.fascin.chatter.bean.event.UIRevitalizeSendEvent
import com.fascin.chatter.bean.event.UISelectModelEvent
import com.fascin.chatter.bean.event.VIpContactRefreshEvent
import com.fascin.chatter.config.Config
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.im.IMModule
import com.fascin.chatter.im.ImNoticeHelper
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.inAppOnlineSwitch
import com.fascin.chatter.main.MainActivity
import com.fascin.chatter.main.adapter.ChattersModelListAdapter
import com.fascin.chatter.main.adapter.ItemTouchHelperCallback
import com.fascin.chatter.main.adapter.removeUnbindModeId
import com.fascin.chatter.main.adapter.sortByMode
import com.fascin.chatter.main.match.MatchFragment
import com.fascin.chatter.main.view.ChatTagSortPopup
import com.fascin.chatter.main.view.ChatTopTipDialog
import com.fascin.chatter.repository.AppRepository
import com.fascin.chatter.utils.ConnectFlagInvisible
import com.fascin.chatter.utils.ConnectFlagMuteMsg
import com.fascin.chatter.utils.ConnectFlagNoOnline
import com.fascin.chatter.utils.LitePalDataHelper
import com.fascin.chatter.utils.getChatFlag
import com.fascin.chatter.utils.getItemActionInfo
import com.fascin.chatter.utils.isFlagEnabled
import com.fascin.chatter.utils.refreshSwitchStatus
import com.google.android.material.tabs.TabLayout
import com.iandroid.allclass.lib_basecore.base.BaseFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.beans.EventMatchFilterChange
import com.iandroid.allclass.lib_common.beans.HomeTabEntity
import com.iandroid.allclass.lib_common.beans.ImOptionItem
import com.iandroid.allclass.lib_common.beans.UIEventClearMatchCountUpdate
import com.iandroid.allclass.lib_common.beans.UIEventNewMatchCountUpdate
import com.iandroid.allclass.lib_common.beans.UserEntity
import com.iandroid.allclass.lib_common.event.CheckHasChatEvent
import com.iandroid.allclass.lib_common.event.ConfigGettedEvent
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.event.EventProfileUpdate
import com.iandroid.allclass.lib_common.event.InAppOnlineSwitchEvent
import com.iandroid.allclass.lib_common.event.RefreshOnlineUIEvent
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeActionByParam
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.DoubleUtils
import com.iandroid.allclass.lib_common.utils.GsonUtils
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.immersiveStatusBarWithPadding
import com.iandroid.allclass.lib_common.utils.exts.jsonToObj
import com.iandroid.allclass.lib_common.utils.exts.show
import com.iandroid.allclass.lib_common.utils.exts.textColorResource
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import com.iandroid.allclass.lib_common.views.CommonAlertDialog
import io.rong.common.RLog
import io.rong.imkit.IMCenter
import io.rong.imkit.RongIM
import io.rong.imkit.config.ConversationListBehaviorListener
import io.rong.imkit.config.RongConfigCenter
import io.rong.imkit.conversationlist.ConversationListAdapter
import io.rong.imkit.conversationlist.model.BaseUiConversation
import io.rong.imkit.conversationlist.model.GatheredConversation
import io.rong.imkit.conversationlist.provider.PrivateConversationProvider
import io.rong.imkit.conversationlist.viewmodel.ConversationListViewModel
import io.rong.imkit.event.Event
import io.rong.imkit.manager.UnReadMessageManager
import io.rong.imkit.model.NoticeContent
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imkit.utils.RouteUtils
import io.rong.imkit.widget.FixedLinearLayoutManager
import io.rong.imkit.widget.adapter.BaseAdapter
import io.rong.imkit.widget.adapter.ViewHolder
import io.rong.imkit.widget.dialog.OptionsPopupDialog
import io.rong.imkit.widget.refresh.constant.RefreshState
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.UserInfo
import kotlinx.android.synthetic.main.fragment_conversationlist.bonusView
import kotlinx.android.synthetic.main.fragment_conversationlist.btnToTop
import kotlinx.android.synthetic.main.fragment_conversationlist.chatsContentRootView
import kotlinx.android.synthetic.main.fragment_conversationlist.chatsStatusPlaceView
import kotlinx.android.synthetic.main.fragment_conversationlist.chatsStatusRootView
import kotlinx.android.synthetic.main.fragment_conversationlist.chatsStatusView
import kotlinx.android.synthetic.main.fragment_conversationlist.chatterActiveTime
import kotlinx.android.synthetic.main.fragment_conversationlist.chatterStatus
import kotlinx.android.synthetic.main.fragment_conversationlist.clChatsTitleBar
import kotlinx.android.synthetic.main.fragment_conversationlist.conTabLayout
import kotlinx.android.synthetic.main.fragment_conversationlist.lineChatNow
import kotlinx.android.synthetic.main.fragment_conversationlist.lineFindVip
import kotlinx.android.synthetic.main.fragment_conversationlist.lineNewMatch
import kotlinx.android.synthetic.main.fragment_conversationlist.llChatNow
import kotlinx.android.synthetic.main.fragment_conversationlist.llFindVip
import kotlinx.android.synthetic.main.fragment_conversationlist.llNewMatch
import kotlinx.android.synthetic.main.fragment_conversationlist.lostOnline
import kotlinx.android.synthetic.main.fragment_conversationlist.lostView
import kotlinx.android.synthetic.main.fragment_conversationlist.matchView
import kotlinx.android.synthetic.main.fragment_conversationlist.noticeContainer
import kotlinx.android.synthetic.main.fragment_conversationlist.pageListView
import kotlinx.android.synthetic.main.fragment_conversationlist.pageModelListView
import kotlinx.android.synthetic.main.fragment_conversationlist.rightNewMsgView
import kotlinx.android.synthetic.main.fragment_conversationlist.rlRevitalize
import kotlinx.android.synthetic.main.fragment_conversationlist.statusViewActionButton
import kotlinx.android.synthetic.main.fragment_conversationlist.statusViewIcon
import kotlinx.android.synthetic.main.fragment_conversationlist.statusViewTitle
import kotlinx.android.synthetic.main.fragment_conversationlist.tvAllChatUnread
import kotlinx.android.synthetic.main.fragment_conversationlist.tvChatNow
import kotlinx.android.synthetic.main.fragment_conversationlist.tvFindVip
import kotlinx.android.synthetic.main.fragment_conversationlist.tvMatchUnread
import kotlinx.android.synthetic.main.fragment_conversationlist.tvNewMatch
import kotlinx.android.synthetic.main.fragment_conversationlist.tvRetry
import kotlinx.android.synthetic.main.fragment_conversationlist.tvRetryLoading
import kotlinx.android.synthetic.main.fragment_conversationlist.tvSelectUnread
import kotlinx.android.synthetic.main.fragment_conversationlist.warningCheckView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/*首页的chat tab分类 //todo mask */
class ConversationFragment : BaseFragment(), BaseAdapter.OnItemClickListener,
    UnReadMessageManager.IUnReadMessageObserver {

    /*
     * 连接通知状态延迟显示时间。
     * 为了防止连接闪断，不会在断开连接时立即显示连接通知状态，而是在延迟一定时间后显示。
     */
    private val NOTICE_SHOW_DELAY_MILLIS = 4000L
    private val TAG = ConversationFragment::class.java.simpleName

    private var mMatchFragment: MatchFragment? = null
    private var mCvFragment: VIPConflateFragment? = null
    var viewModel: ChatsViewModel? = null
    var mAdapter: ConversationListAdapter? = null
    var recyclerViewSupport: RecyclerViewSupport? = null
    private var modelAdapter: ChattersModelListAdapter? = null
    var mConversationListViewModel: ConversationListViewModel? = null
    private var modelList: ArrayList<ModelUserEntity> = ArrayList()
    private var conversationTabList: ArrayList<HomeTabEntity> = ArrayList()
    private val mHandler = Handler(Looper.getMainLooper())
    var mNewState = RecyclerView.SCROLL_STATE_IDLE
    var delayRefresh = false
    var emptyView: View? = null
    var curTabView: TextView? = null
    var conversationChatsTab: Int = ChatsConfig.FlagTabAllChats
    private lateinit var countDownTimer: CountDownTimer
    private var elapsedTimeSeconds: Long = 0

    // 最近一次获取model列表数据的时间
    private var getModelTime: Long = -1L

    // vip老用户召回最大倒计时
    private var revitalizeTotalTime = 5 * 60 * 1000L
    private var isTimerRunning = false
    private var isGetTop = false
    private var isRemoveModel = false
    private var statusIsActive = false
    private var initConversationFailed = false

    // 删除不属于任何model的会话
    private var isRemoveConForModel = false

    // 当前选中的排序tabLayout标签id,切换model的时候，重置
    private var filterTabID = TabConfig.conversionAll

    // 优先靠前tag
    private var tagSortById: Int = ChatsConfig.TAG_NO_SEL

    private var isBonusMatchHubShowing: Boolean = true
    private var leftTime: Int = 0

    init {
        mAdapter = onResolveAdapter()
        RongConfigCenter.conversationListConfig().providerManager.replaceProvider(
            PrivateConversationProvider::class.java, ConversationProvider()
        )
    }

    /**
     * 获取 adapter. 可复写此方法实现自定义 adapter.
     *
     * @return 会话列表 adapter
     */
    private fun onResolveAdapter(): ConversationListAdapter {
        return ConversationListAdapter()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (!IMCenter.getInstance().isInitialized) {
            Log.e(TAG, "Please init SDK first!")
            return
        }

        mMatchFragment = childFragmentManager.findFragmentById(R.id.matchLayout) as MatchFragment
        initVIPContactsFragment()
        viewModel = ViewModelProvider(this).get(ChatsViewModel::class.java)
        immersiveStatusBarWithPadding(clChatsTitleBar)
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, pageListView, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    onConversationListRefresh()
                }

                override fun onFooterRefresh() {
                    onConversationListLoadMore()
                }
            })
        }

        chatterActiveHours()

        pageModelListView.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        modelAdapter = ChattersModelListAdapter { userId, reClick ->
            // 当前不是match页时，并以是该model时，跳model主页
            if (reClick && conversationChatsTab != ChatsConfig.FlagTabMatch) {
                context.routeActionByParam<UserEntity>(ActionType.actionOtherProfile) {
                    it.userId = userId
                }
            } else {
                // 当前是match页时，切换到all chat页
                if (conversationChatsTab == ChatsConfig.FlagTabMatch) {
                    conversationChatsTab = ChatsConfig.FlagTabAllChats
                    setSelectedTab(chatNowSelected = true)
                }
                setCurrTag()
                filterConversationList(true)
                btnToTop.show(false)
            }
        }

        // New Match
        llNewMatch.setOnClickListener {
            conversationChatsTab = ChatsConfig.FlagTabMatch
            pageListView.show(false)
            setTabChangeUI()
            AppRepository.eventTrace(EventKey.K_tabs_match)
            setCurrTag()
        }
        // Chat Now
        llChatNow.setOnClickListener {
            conversationChatsTab = ChatsConfig.FlagTabAllChats
            setTabChangeUI()
            AppRepository.eventTrace(EventKey.K_tabs_allchats)
            setCurrTag()
        }
        // Find Vip(前 Long Lost)
        llFindVip.setOnClickListener {
            // 上报
            if (conversationChatsTab != ChatsConfig.FlagTabLongLost) CommonRepository.eventTrace(
                EventKey.K_tab_ll_c
            )
            conversationChatsTab = ChatsConfig.FlagTabLongLost
            setTabChangeUI()
            setCurrTag()
        }

        setSelectedTab(chatNowSelected = true)
        setTabChangeUI()
        initChatsTab()
        pageModelListView.adapter = modelAdapter!!

        val itemTouchHelperCallback = ItemTouchHelperCallback(modelAdapter!!)
        val itemTouchHelper = ItemTouchHelper(itemTouchHelperCallback)
        itemTouchHelper.attachToRecyclerView(pageModelListView)

        setEmptyView()
        mAdapter?.setItemClickListener(this)
        pageListView.setLayoutManager(FixedLinearLayoutManager(activity))
        pageListView.setAdapter(mAdapter)
        pageListView.mRecyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(
                recyclerView: RecyclerView, newState: Int,
            ) {
                mNewState = newState
                if (mNewState == RecyclerView.SCROLL_STATE_IDLE && delayRefresh && mAdapter != null && mConversationListViewModel != null) { // 滚动停止
                    delayRefresh = false
                    filterConversationList()
                }
                if (mNewState == RecyclerView.SCROLL_STATE_IDLE) {
                    showScrollToTopView()
                }
            }
        })

        // 按未读数分组，有未读数的排前面
        tvSelectUnread.clickWithTrigger {
            if (filterTabID != TabConfig.conversionUnread) {
                clickUnlockFilterTrace("Unread")
                // conTabLayout子tab与tvSelectUnread是单选的需求，所以要重置
                curTabView?.isSelected = false
                curTabView?.textColorResource = com.iandroid.allclass.lib_common.R.color.cl_595959
                tvSelectUnread.isSelected = true
                tvSelectUnread.textColorResource = R.color.white
                filterTabID = TabConfig.conversionUnread
                tagSortById = ChatsConfig.TAG_NO_SEL
                filterConversationList()
            }
        }

        btnToTop.clickWithTrigger {
            // 滚动到顶部
            recyclerViewSupport?.scrollToTop()
            btnToTop?.show(false)
        }

        //首页排版入口隐藏
//        shiftEntranceView.clickWithTrigger {
//            viewModel?.readNewSchedule()
//            shiftEntranceView.hasChange(false)
//            // 跳转排班界面
//            context.routeAction(ActionType.actionSchedule)
//        }

        RongIM.setConversationListBehaviorListener(object : ConversationListBehaviorListener {
            override fun onConversationPortraitClick(
                context: Context?,
                conversationType: Conversation.ConversationType?,
                targetId: String?,
            ): Boolean {
                context?.routeActionByParam<UserEntity>(ActionType.actionOtherProfile) {
                    it.userId = targetId.orEmpty()
                }
                return true
            }

            override fun onConversationPortraitLongClick(
                context: Context?,
                conversationType: Conversation.ConversationType?,
                targetId: String?,
            ): Boolean {
                return false
            }

            override fun onConversationLongClick(
                context: Context?, view: View?, conversation: BaseUiConversation?,
            ): Boolean {
                return false
            }

            override fun onConversationClick(
                context: Context?, view: View?, conversation: BaseUiConversation?,
            ): Boolean {
                return false
            }
        })
        subscribeUi()

        pageModelListView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    findFirstUnreadPosition()
                }
            }
        })
        initBonusTipView()
        initWarningTipView()
    }

    /**
     * 初始化VIPContactsFragment
     */
    private fun initVIPContactsFragment() {
//        mCvFragment = childFragmentManager.findFragmentById(R.id.lostLayout) as VIPContactsFragment
        mCvFragment = childFragmentManager.findFragmentById(R.id.lostLayout) as VIPConflateFragment
//        mCvFragment = VIPConflateFragment()
//        childFragmentManager.beginTransaction().add(R.id.lostView, mCvFragment!!).commit()
    }

    private fun setTabChangeUI() {
        when (conversationChatsTab) {
            ChatsConfig.FlagTabAllChats -> {
                AppModule.isMatchTabShow = false
                setEmptyView()
                filterConversationList()
                matchView.show(false)
                pageListView.show(true)
                lostView?.show(false)
                setSelectedTab(chatNowSelected = true)
            }

            ChatsConfig.FlagTabMatch -> {
                AppModule.isMatchTabShow = true
                // 切换到match时，清空match未读数
                SimpleRxBus.post(UIEventClearMatchCountUpdate())
                SimpleRxBus.post(UIEventNewMatchCountUpdate(0))
                matchView?.show(true)
                pageListView?.show(false)
                lostView?.show(false)
                setSelectedTab(newMatchSelected = true)
            }

            ChatsConfig.FlagTabLongLost -> {
                AppModule.isMatchTabShow = false
                matchView.show(false)
                pageListView.show(false)
                lostView?.show(true)
                setSelectedTab(findVipSelected = true)
            }
        }
    }

    private fun setSelectedTab(
        newMatchSelected: Boolean = false,
        chatNowSelected: Boolean = false,
        findVipSelected: Boolean = false
    ) {
        tvChatNow.textColorResource =
            if (chatNowSelected) R.color.black else com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
        lineChatNow.show(chatNowSelected, true)
        tvNewMatch.textColorResource =
            if (newMatchSelected) R.color.black else com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
        lineNewMatch.show(newMatchSelected, true)
        tvFindVip.textColorResource =
            if (findVipSelected) R.color.black else com.iandroid.allclass.lib_common.R.color.cr_8c8c8c
        lineFindVip.show(findVipSelected, true)
    }

    private fun initChatsTab() {
        conversationTabList = TabConfig.getConversationTabList()

        for (index in conversationTabList.indices) {
            val tab: TabLayout.Tab = conTabLayout.newTab()
            tab.setCustomView(R.layout.layout_con_tab_text)
            tab.customView?.run {
                val tabItemViewTitle = findViewById<TextView>(R.id.tvTabName)
                tabItemViewTitle.text = conversationTabList[index].title
                if (index == 0) {
                    tabItemViewTitle.isSelected = true
                    tabItemViewTitle.textColorResource = R.color.white
                    curTabView = tabItemViewTitle
                }

                val iconView = findViewById<ImageView>(R.id.ivTabIcon)
                val icon = conversationTabList[index].icon
                if (icon != null) {
                    iconView.setImageDrawable(icon)
                    iconView.show(true)
                } else {
                    iconView.show(false)
                }
            }
            conTabLayout.addTab(tab)
        }

        conTabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val position = tab?.position ?: 0
                // conTabLayout子tab与tvSelectUnread是单选的需求，所以要重置
                if (filterTabID == TabConfig.conversionUnread) {
                    tvSelectUnread.isSelected = false
                    tvSelectUnread.textColorResource =
                        com.iandroid.allclass.lib_common.R.color.cl_595959
                }
                filterTabID = conversationTabList[position].id
                tagSortById = ChatsConfig.TAG_NO_SEL
                clickUnlockFilterTrace(conversationTabList[position].title)
                tab?.customView?.run {
                    val tabTitle = findViewById<TextView>(R.id.tvTabName)
                    tabTitle.isSelected = true
                    tabTitle.textColorResource = R.color.white
                    curTabView = tabTitle
                    if (filterTabID == TabConfig.conversionType) {
                        ChatTagSortPopup.showPopupMenu(context, this, tagSortById) {
                            tagSortById = it
                            filterConversationList()
                        }
                    } else {
                        filterConversationList()
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    val tabTitle = findViewById<TextView>(R.id.tvTabName)
                    tabTitle.isSelected = false
                    tabTitle.textColorResource = com.iandroid.allclass.lib_common.R.color.cl_595959
                }
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // conTabLayout子tab与tvSelectUnread是单选的需求，选中Unread 时，再选之前选中tab，会触发此处,需更新UI
                if (filterTabID == TabConfig.conversionUnread) {
                    val position = tab?.position ?: 0
                    filterTabID = conversationTabList[position].id
                    tagSortById = ChatsConfig.TAG_NO_SEL
                    curTabView?.isSelected = true
                    curTabView?.textColorResource = R.color.white
                    tvSelectUnread.isSelected = false
                    tvSelectUnread.textColorResource =
                        com.iandroid.allclass.lib_common.R.color.cl_595959
                    if (filterTabID == TabConfig.conversionType) {
                        tab?.customView?.let { customView ->
                            ChatTagSortPopup.showPopupMenu(context, customView, tagSortById) {
                                tagSortById = it
                                filterConversationList()
                            }
                        }
                    } else {
                        filterConversationList()
                    }
                } else if (filterTabID == TabConfig.conversionType) {
                    tab?.customView?.let { customView ->
                        ChatTagSortPopup.showPopupMenu(context, customView, tagSortById) {
                            tagSortById = it
                            filterConversationList()
                        }
                    }
                }
            }
        })

    }

    private fun filterConversationList(fromDataUpdate: Boolean = false) {
        val curSelModelUserId = modelAdapter?.curSelUserId.orEmpty()
        val conversationValues = getConversationValue()
        if (curSelModelUserId.isNullOrEmpty() || conversationValues?.isNullOrEmpty() == true) {
            mAdapter?.setDataCollection(null)
        } else {
            val modelUnreadMap = HashMap<String, Int>()
            conversationValues?.also {
                val filterList = ArrayList<BaseUiConversation>()
                for (item in it) {
                    val modelId = getModelId(item.conversationIdentifier.targetId.orEmpty())
                    val isAiChat =
                        AiAutoChatHelper.isAiImId(item.conversationIdentifier.targetId.orEmpty())
                    if (!isAiChat && fromDataUpdate && item.mCore.unreadMessageCount > 0) {
                        //已解除绑定的model,但是还有会话未读数，直接标记为已读
                        if (modelId.isNotEmpty() && modelAdapter?.modelList?.any { models -> models.userId == modelId } == false) {
                            IMCenter.getInstance()
                                .clearMessagesUnreadStatus(item.conversationIdentifier, null)
                        }
                        if (modelId.isNotEmpty() && item.mCore.notificationStatus.equals(
                                Conversation.ConversationNotificationStatus.NOTIFY
                            )
                        ) {
                            if (modelUnreadMap.containsKey(modelId)) {
                                val curUnread = modelUnreadMap[modelId] ?: 0
                                modelUnreadMap[modelId] = curUnread + item.mCore.unreadMessageCount
                            } else {
                                modelUnreadMap[modelId] = item.mCore.unreadMessageCount
                            }
                        }
                    }
                    if (item.conversationIdentifier != null && item.conversationIdentifier.targetId.startsWith(
                            curSelModelUserId
                        )
                    ) {
                        if (conversationChatsTab == ChatsConfig.FlagTabAllChats) {
                            // 当前是查看model所有会话时
                            filterList.add(item)
                        }
                    }
                }

                if (fromDataUpdate) {
                    //更新model的未读数
                    modelAdapter?.updateModelUnread(modelUnreadMap)
                    findFirstUnreadPosition()
                    updateChatsUnread(modelUnreadMap)
                }
                lifecycleScope.launch(Dispatchers.Main) {
                    val sortedList = sortChatsUser(filterList)
                    mAdapter?.setDataCollection(sortedList)
                    setModelOnlineNum(it)
                }
                it?.forEach { filterItem ->
                    filterItem?.mCore?.targetId?.let { targetId ->
                        IMModule.instance.queryUserSimpleInfo(targetId)
                    }
                }
            }
        }
    }

    /**
     * 根据筛选条件对会话列表重新排序 默认在线在前
     */
    private suspend fun sortChatsUser(chatList: List<BaseUiConversation>): List<BaseUiConversation> =
        withContext(Dispatchers.Default) {
            try {
                val filterChatsConversation = filterChatsConversation(chatList)
                filterChatsConversation.forEach { item ->
                    val privacyUnlockData =
                        LitePalDataHelper.getPrivacyUnlockData(item.mCore.targetId) { e ->
                            val exception = e.javaClass.name + ":" + e.message
                            viewModel?.traceErrorOrTimeout(
                                exception = exception,
                                funName = "getPrivacyUnlockData"
                            )
                        }
                    privacyUnlockData?.let { unlock ->
                        item.unlockEntity = unlock
                    }
                }
                val sortedList =
                    filterChatsConversation.sortedWith(compareByDescending<BaseUiConversation> {
                        calculateComparisonValue(it)
                    }.thenByDescending {
                        // 先按置顶排
                        it.mCore.isTop
                    }.thenByDescending {
                        // 再按在线状态
                        UserOnlineHelper.isOnline(it.mCore.targetId)
                    }.thenByDescending {
                        //再按 最近活跃的排
                        val userInfo =
                            RongUserInfoManager.getInstance().getUserInfo(it.mCore.targetId)
                        val userImExtra = userInfo?.extra?.jsonToObj<UserImExtraEntity>()
                        userImExtra != null && UserOnlineHelper.isActive(
                            it.mCore.targetId,
                            userImExtra.lastActiveTime
                        )
                    }.thenByDescending {
                        // 如果开启了解锁数排序，再按解锁数
                        val unlockNum: Int =
                            if (it.unlockEntity is PrivacyUnlockDBEntity) (it.unlockEntity as PrivacyUnlockDBEntity).unlockNum else 0
                        unlockNum
                    }.thenByDescending {
                        it.mCore.sentTime
                    })

                sortedList
            } catch (e: Exception) {
                val exception = e.javaClass.name + ":" + e.message
                viewModel?.traceErrorOrTimeout(exception = exception, funName = "sortChatsUser")
                filterChatsConversation(chatList)
            }
        }

    /**
     * 是否是符合tag排序的会话
     */
    private fun isTagSortConversion(userImExtra: UserImExtraEntity): Boolean {
        if (tagSortById == ChatsConfig.TAG_NO_SEL) return false
        return userImExtra?.contactTag == tagSortById
    }

    private fun calculateComparisonValue(chatItem: BaseUiConversation): Comparable<Int> {
        if (filterTabID == TabConfig.conversionAll) {
            // 不做任何筛选时
            return 1
        }
        // 按未读数筛选时，有未读数的都排在前面
        if (filterTabID == TabConfig.conversionUnread) {
            return if (chatItem.mCore.unreadMessageCount > 0) 1 else 0
        }
        val userInfo = RongUserInfoManager.getInstance().getUserInfo(chatItem.mCore.targetId)
        val userImExtra = userInfo?.extra?.jsonToObj<UserImExtraEntity>()
        userImExtra?.let { extra ->
            val primeFilter = filterTabID == TabConfig.conversionPrime && extra.tagPrime == 1
            val risingFilter = filterTabID == TabConfig.conversionRising && extra.tagRising == 1
            val tagFilter = filterTabID == TabConfig.conversionType && isTagSortConversion(extra)
            val unlockFilter = filterTabID == TabConfig.conversionUnlock && extra.unlockNum > 0
            val newIceFilter = filterTabID == TabConfig.conversionNewChat && extra.newIceBeak == 1

            return when {
                primeFilter || risingFilter || tagFilter || unlockFilter || newIceFilter -> 1
                else -> 0
            }
        }
        return 0
    }

    /**
     * 根据isChatShowMatch过滤用户没有回复消息会话
     * 用户没有给model发过消息时：
     *    1.isChatShowMatch开：主播发过消息的会话显示在会话列表
     *    2.isChatShowMatch关：主播发过消息的会话不显示在会话列表，显示在match列表
     */
    private suspend fun filterChatsConversation(chatList: List<BaseUiConversation>): List<BaseUiConversation> {
        return chatList.filter { data ->
            withContext(Dispatchers.IO) {
                val userInfo = RongUserInfoManager.getInstance().getUserInfo(data.mCore.targetId)
                val userImExtra = userInfo?.extra?.let {
                    GsonUtils.fromJson(it, UserImExtraEntity::class.java)
                }
                val lastIsReceive =
                    data.mCore.latestMessageDirection == io.rong.imlib.model.Message.MessageDirection.RECEIVE
                val lastIsSend =
                    data.mCore.latestMessageDirection == io.rong.imlib.model.Message.MessageDirection.SEND
                val lastIsConnectType = data.mCore.objectName.equals("connectfriends:connected")
                val isAiChat = AiAutoChatHelper.isAiImId(data.mCore.targetId.orEmpty())
                // 不是ai会话，且 用户发消息数大于0，或是收到的消息，并且不是建联的消息
                !isAiChat && (((userImExtra?.userChatNum ?: 0) > 0)
                        || (lastIsReceive && !lastIsConnectType)
                        || (AppController.isChatShowMatch() && lastIsSend && !lastIsConnectType))
            }
        }
    }

    /**
     * 设置model下在线会话数
     */
    private suspend fun setModelOnlineNum(list: List<BaseUiConversation>) {
        val onlineNum = HashMap<String, Int>()
        val activeNum = HashMap<String, Int>()
        try {
            withContext(Dispatchers.IO) {
                filterChatsConversation(list)?.forEach { item ->
                    // 记录各model下是否有在线、活跃的会话
                    if (item.conversationIdentifier != null) {
                        val modelId = getModelId(item.conversationIdentifier.targetId.orEmpty())
                        if (modelId.isNotEmpty()) {
                            if (UserOnlineHelper.isOnline(item.conversationIdentifier.targetId)) {
                                if (onlineNum.containsKey(modelId)) {
                                    onlineNum[modelId] = onlineNum[modelId]!! + 1
                                } else {
                                    onlineNum[modelId] = 1
                                }
                            } else {
                                RongUserInfoManager.getInstance().getUserInfo(item.mCore.targetId)
                                    ?.also { userInfo ->
                                        userInfo.extra?.jsonToObj<UserImExtraEntity>()
                                            ?.let { extra ->
                                                if (UserOnlineHelper.isActive(
                                                        item.conversationIdentifier.targetId,
                                                        extra.lastActiveTime
                                                    )
                                                ) {
                                                    if (activeNum.containsKey(modelId)) {
                                                        activeNum[modelId] =
                                                            activeNum[modelId]!! + 1
                                                    } else {
                                                        activeNum[modelId] = 1
                                                    }
                                                }
                                            }.toJsonString()
                                    }
                            }
                        }
                    }
                }
            }
            modelAdapter?.setImOnlineStatus(onlineNum, activeNum)
        } catch (e: Exception) {
            val exception = e.javaClass.name + ":" + e.message
            viewModel?.traceErrorOrTimeout(exception = exception, funName = "setModelOnlineNum")
        }
    }

    private fun getModelId(imUserId: String): String {
        var targetIdIndex = imUserId.indexOf("_")
        if (targetIdIndex > 0) {
            return imUserId.substring(0, targetIdIndex)
        }
        return ""
    }

    /**
     * 设置tab未读数
     */
    private fun setUnreadText(allUnread: Int) {
        tvAllChatUnread?.text = if (allUnread <= 99) allUnread.toString() else "99+"
        tvAllChatUnread?.visibility = if (allUnread <= 0) View.GONE else View.VISIBLE
    }

    /**
     * 设置tab及main左下角未读数
     */
    private fun updateChatsUnread(unreadData: HashMap<String, Int>) {
        var count = 0
        unreadData.forEach { (_, value) ->
            count += value.coerceAtLeast(0)
        }
        setUnreadText(count)
        getMainPage()?.onMsgCountChanged(count)
    }

    /**
     * 观察 view model 各数据以便进行页面刷新操作。
     */
    private fun subscribeUi() {
        RongIM.getInstance().addUnReadMessageCountChangedObserver(
            this, Conversation.ConversationType.PRIVATE
        )
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIEventUserBlock::class) {
            //发生了拉黑行为 ，清理列表
            IMModule.instance.delConversation(it.userId)
        })

        //解绑&&绑定事件
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ChatterBindUpdateEntity::class) { modeUpdate ->
            //解除绑定清理排序数据
            modeUpdate.takeIf { it.bind == 0 }?.run {
                modelids?.let { removeUnbindModeId(it) }
            }
            // 是否是删除
            isRemoveModel = modeUpdate.bind == 2
            getModels()
            SimpleRxBus.post(EventProfileUpdate())
        })

        // 更新match新消息数
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIEventNewMatchCountUpdate::class) {
            setMatchTag(it.newMatchCount)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(EventMatchFilterChange::class) {
            // match筛选状态发生变化时

        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RefreshOnlineUIEvent::class) {
            // 刷新列表
            filterConversationList()
            // vip contact是否有在线的
            lostOnline.show(mCvFragment?.hasOnline() == true)
            setMatchTag()
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(InAppOnlineSwitchEvent::class) {
            filterConversationList()
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(CheckHasChatEvent::class) {
            // 检查是否需要拉取用户聊天记录到列表,等待3s是为了等待setUserInfoProvider中queryUserSimpleInfo拉取userinfo
            postDelayed({
                checkHasChat(it.userIds)
            }, 3000)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIContactTagChangeEvent::class) {
            // 更新联系人标签
            contactTagChange(it.targetId, it.tagId)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(VIpContactRefreshEvent::class) {
            // vip contact是否有在线的
            lostOnline.show(mCvFragment?.hasOnline() == true)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIAiChatChangeEvent::class) {
            // ai会话发生了变化,刷新UI
            filterConversationList(true)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIAiChatChangeEvent::class) {
            // ai会话发生了变化,刷新UI
            filterConversationList(true)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIFlashChatNumChangeEvent::class) {
            // fc剩余次数发生了变化,刷新UI
            filterConversationList(true)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ConversationRefreshEvent::class) {
            // 刷新列表
            filterConversationList()
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIRevitalizeSendEvent::class) {
            // 开启vip老用户召回入口倒计时
            rlRevitalize.startDownTimer(revitalizeTotalTime)
        })

        // 配置接口请求完成
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ConfigGettedEvent::class) {
            showRevitalizeIn()
        })

        // 老用户召回入口开关发生变化
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RevitalizeInChangeEntity::class) {
            if (AppController.showRecallVipBtn() == it.showRecallVipBtn) return@observe
            AppController.updateGlobalDataConfig(
                AppController.getGlobalDataConfig().also { globalConfigEntity ->
                    globalConfigEntity.showRecallVipBtn = it.showRecallVipBtn
                })
            showRevitalizeIn()
        })

        //主播分等级变更通知
        //Removed by ShenChao, 移动到了profile页面
//        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ChatterLevelEntity::class) {
//            tvFraction.show(it.level.isNotEmpty())
//            tvFraction.text = it.level.orEmpty()
//        })

        // 添加了model
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UISelectModelEvent::class) {
            getModels()
        })

        // 定向建联状态发生变化展示
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(BonusTipEntity::class) {
            viewModel?.getBonusTip()
        })

        viewModel?.chatterModelLiveData?.observe(viewLifecycleOwner) {
            getModelTime = System.currentTimeMillis()
            setLoadingUI(false)
            chatsStatusRootView.show(it.isNullOrEmpty())
            chatsContentRootView.show(!it.isNullOrEmpty())
            if (it.isNullOrEmpty()) {
                updateStatusView(
                    R.drawable.ic_coversation_empty, getString(R.string.coversation_empty)
                )
            }
            modelList = sortByMode(it)
            ChatsConfig.allModelIdAndHead.clear()
            it.forEach { model ->
                ChatsConfig.allModelIdAndHead[model.userId] = model.avatarUrl
            }
//            mContactFragment?.setModelList(modelList, isRemoveModel)
            modelAdapter?.updateData(modelList, isRemoveModel)
            // 删除model触发刷新
            if (isRemoveModel) {
                setCurrTag()
                filterConversationList(true)
                isRemoveModel = false
            }
            updateModelPortrait(it)
        }

        viewModel?.chatterErrorMsg?.observe(viewLifecycleOwner) {
            getModelTime = System.currentTimeMillis()
            setLoadingUI(false)
            if (modelList.isNullOrEmpty()) {
                chatsStatusRootView.show(true)
                chatsContentRootView.show(false)
                updateStatusView(R.mipmap.ic_page_error, it)
            }
        }

        viewModel?.aiAutoChatLiveData?.observe(viewLifecycleOwner) {
            AiAutoChatHelper.resetAiImIdList(it)
            if (it.isNotEmpty()) {
                // 获取了ai会话数据，更新UI
                filterConversationList(true)
            }
        }

        viewModel?.aiAutoChatError?.observe(viewLifecycleOwner) {
            // 获取了ai会话数据失败
//            ToastUtils.showToast("get AI Chat error!")
        }

        viewModel?.revitalizeInResult?.observe(viewLifecycleOwner) {
            revitalizeTotalTime = it.totalTime.times(1000L)
            rlRevitalize.show(AppController.showRecallVipBtn() == 1)
            rlRevitalize.setRevitalizeData(it)
        }

        //排版更新到profile页面处理
        viewModel?.shiftsInResult?.observe(viewLifecycleOwner) {
//            shiftEntranceView.setData(it)

            SimpleRxBus.post(ChatterShiftEntity(it))
        }

        viewModel?.getAiAutoChatId()
        viewModel?.shiftEntranceData()
        getModels()
        // 从服务端获取最近会话列表数据
        getInitConversations()
        // 启动获取上线数据轮询
        if (AppController.systemConfigOk)
            showRevitalizeIn()

        setConversionCountPerPage()
        // 会话列表数据监听
        mConversationListViewModel = ViewModelProvider(this).get(
            ConversationListViewModel::class.java
        ).also {
            it.getConversationList(false, false, 0)
            it.conversationListLiveData.observe(viewLifecycleOwner) { uiConversations ->
                if (mNewState == RecyclerView.SCROLL_STATE_IDLE) {
                    filterConversationList(true)
                } else {
                    delayRefresh = true
                }
                deleteConversationOnNoModel()
            }

            it.sysNoticeAccount = AppController.getNoticeAccount()

            // 连接状态监听
            it.noticeContentLiveData.observe(viewLifecycleOwner) { noticeContent ->
                // 当连接通知没有显示时，延迟进行显示，防止连接闪断造成画面闪跳。
                if (noticeContainer.visibility == View.GONE) {
                    mHandler.postDelayed(
                        { // 刷新时使用最新的通知内容
                            updateNoticeContent(mConversationListViewModel?.noticeContentLiveData?.value)
                        }, NOTICE_SHOW_DELAY_MILLIS
                    )
                } else {
                    updateNoticeContent(noticeContent)
                }
                noticeContainer?.findViewById<TextView>(io.rong.imkit.R.id.btnReconnect)
                    ?.show(false)
                // IM账号被挤下线时，退到登录页
                if (noticeContent.isLogoutByOtherClient) {
                    logoutByOtherClient()
                }
            }

            // 刷新事件监听
            it.refreshEventLiveData.observe(viewLifecycleOwner) { refreshEvent: Event.RefreshEvent ->
                if (refreshEvent.state == RefreshState.LoadFinish) {
                    recyclerViewSupport?.onFooterRefreshComplete()
                    recyclerViewSupport?.onHeaderRefreshComplete()
                } else if (refreshEvent.state == RefreshState.RefreshFinish) {
                    recyclerViewSupport?.onFooterRefreshComplete()
                    recyclerViewSupport?.onHeaderRefreshComplete()
                }
            }

            // 收到了或发送了消息，需要刷新model对应的数据
            it.getSendOrReceivedLiveData().observe(viewLifecycleOwner) { targetId ->
//                mContactFragment?.setModelRefreshFlag(targetId)
                mCvFragment?.setChatRefreshFlag(targetId)
                mMatchFragment?.chatChange(targetId)
            }
        }
    }

    /**
     * 根据配置设置会话列表页首次获取的最大数据数
     */
    private fun setConversionCountPerPage() {
        val countPerPage = AppController.getConversationCountPerPage()
        RongConfigCenter.conversationListConfig()?.setCountPerPage(countPerPage)
    }

    override fun onResume() {
        super.onResume()
        mConversationListViewModel?.clearAllNotification()
        setCurrTag()
        // 每隔10分钟，刷新一次model列表
        if (getModelTime > 0 && ((System.currentTimeMillis() - getModelTime) > 10 * 60 * 1000L)) {
            getModels()
        }
        // 获取失败时重新获取
        if (initConversationFailed) getInitConversations()
    }

    override fun onStop() {
        super.onStop()
        setCurrTag(true)
    }

    fun onConversationListRefresh() {
//        mConversationListViewModel?.getConversationList(false, true, 0)
        // 列表实时刷新，又不想让主播手动刷新，模拟刷新一下
        postDelayed({
            recyclerViewSupport?.onFooterRefreshComplete()
            recyclerViewSupport?.onHeaderRefreshComplete()
        }, 500)
    }

    fun onConversationListLoadMore() {
        mConversationListViewModel?.getConversationList(true, true, 0)
    }

    /**
     * 更新连接状态通知栏
     * @param content
     */
    private fun updateNoticeContent(content: NoticeContent?) {
        content?.also {
            offLineStatus(it.isShowNotice)
            if (it.isShowNotice) {
                noticeContainer.show(true)
                noticeContainer?.findViewById<TextView>(io.rong.imkit.R.id.rc_conversationlist_notice_tv)?.text =
                    content.content
                if (content.iconResId != 0) {
                    noticeContainer?.findViewById<ImageView>(io.rong.imkit.R.id.rc_conversationlist_notice_icon_iv)
                        ?.setImageResource(content.iconResId)
                }
            } else {
                noticeContainer.show(false)
                if (!isGetTop) {
                    isGetTop = true
                    // 获取所有置顶数据
                    ChatTopHelper.resetTops(true)
                }
            }
        }
    }

    /**
     * 登录的chatter账号正常时，IM账号被挤下线，需要提供重连入口。
     * 场景：
     * 1.多设备登录过，token失效，启动app，refreshToken还未请求完，IM就登录了，导致最近登录的设备IM被挤；
     * 2.在另一台设备登录时，原登录设备恰好未触发被挤号的指令（如无网导致融云断开、未调用接口没收到code 1002等），
     * 网络恢复时，IM重连，导致新登录的设备IM被挤下线。
     */
    private fun logoutByOtherClient() {
//                    imLogoutTrace()
        ToastUtils.showToast("This account has been logged-in on other devices")
//                    UserController.tickOffline()
        noticeContainer?.findViewById<TextView>(io.rong.imkit.R.id.btnReconnect)
            ?.also { btnReconnect ->
                btnReconnect.show(true)
                btnReconnect.clickWithTrigger {
                    RongIM.getInstance().disconnect()
                    RongIM.getInstance().logout()
                    AppController.userLogin()
                    btnReconnect.show(false)
                }
            }
    }

    /**
     * 将所有model的信息缓存起来，主要是头像
     */
    private fun updateModelPortrait(modelUserEntities: ArrayList<ModelUserEntity>) {
        modelUserEntities.forEach {
            // 更新聊天信息中的头像为当前model头像
            val userInfo = UserInfo(it.userId, it.nickname, Uri.parse(it.avatarUrl)).apply {
                val userInfoCache = RongUserInfoManager.getInstance().getUserInfo(it.userId)
                if (userInfoCache != null && !userInfoCache.extra.isNullOrEmpty()) {
                    val oldUserImExtra =
                        GsonUtils.fromJson(userInfoCache.extra, UserImExtraEntity::class.java)
                    extra = oldUserImExtra?.toJsonString()
                }
            }
            RongUserInfoManager.getInstance().refreshUserInfoCache(userInfo)
        }
    }

    /**
     * 设置match tab红点绿点的显示
     */
    private fun setMatchTag(newMatchCount: Int = 0) {
        mMatchFragment?.let { matchPage ->
            val hasTabTag = matchPage.hasTabTag()
            tvMatchUnread?.show(hasTabTag >= 0 || newMatchCount > 0)
            tvMatchUnread?.setBackgroundResource(
                if (hasTabTag == 1 || newMatchCount > 0) {
                    R.drawable.bg_f74e57_strole_white_r99
                } else if (hasTabTag == 0) {
                    R.drawable.bg_24c004_strole_white_r99
                } else {
                    0
                }
            )
        }
    }

    private fun checkHasChat(userIds: ArrayList<String>?) {
        userIds?.forEach { userId ->
            getConversationValue()?.let { chats ->
                // 无会话时，获取会话
                if (!chats.any { chat -> chat.conversationIdentifier?.targetId == userId }) {
                    RongUserInfoManager.getInstance()
                        .getUserInfo(userId)?.extra?.jsonToObj<UserImExtraEntity>()
                        ?.let { user ->
                            if (!isFlagEnabled(
                                    user.chatFlag,
                                    ConnectFlagNoOnline
                                ) && !isFlagEnabled(
                                    user.chatFlag,
                                    ConnectFlagInvisible
                                )
                            ) {
                                val belongCurModel =
                                    modelAdapter?.curSelUserId?.isNotEmpty() == true && userId.contains(
                                        modelAdapter?.curSelUserId!!
                                    )
                                mConversationListViewModel?.getTargetHistoryMsg(
                                    userId,
                                    belongCurModel
                                )
                            }
                        }
                }
            }
        }
    }

    /**
     * 会话列表点击事件回调
     *
     * @param view     点击 view
     * @param holder   [ViewHolder]
     * @param position 点击位置
     */
    override fun onItemClick(view: View, holder: ViewHolder?, position: Int) {
        // 防连点
        if (DoubleUtils.isFastDoubleClick()) return
        if (position < 0 || position >= mAdapter?.data?.size!!) return
        val baseUiConversation = mAdapter?.getItem(position)
        val listBehaviorListener = RongConfigCenter.conversationListConfig().listener
        if (listBehaviorListener != null && listBehaviorListener.onConversationClick(
                view.context, view, baseUiConversation
            )
        ) {
            RLog.d(TAG, "ConversationList item click event has been intercepted by App.")
            return
        }
        if (baseUiConversation?.mCore != null) {
            if (baseUiConversation is GatheredConversation) {
                RouteUtils.routeToSubConversationListActivity(
                    view.context,
                    baseUiConversation.mGatheredType,
                    baseUiConversation.mCore.conversationTitle
                )
            } else {
                RouteUtils.routeToConversationActivity(
                    view.context, baseUiConversation.conversationIdentifier
                )
            }
        } else {
            RLog.e(TAG, "invalid conversation.")
        }
    }

    /**
     * 会话列表长按事件回调
     *
     * @param view     点击 view
     * @param holder   [ViewHolder]
     * @param position 点击位置
     * @return 事件是否被消费
     */
    override fun onItemLongClick(view: View, holder: ViewHolder?, position: Int): Boolean {
        if (position < 0 || position >= mAdapter?.data?.size!!) {
            return false
        }
        val baseUiConversation = mAdapter!!.getItem(position)
        val listBehaviorListener = RongConfigCenter.conversationListConfig().listener
        if (listBehaviorListener != null && listBehaviorListener.onConversationLongClick(
                view.context, view, baseUiConversation
            )
        ) {
            RLog.d(TAG, "ConversationList item click event has been intercepted by App.")
            return true
        }
        val targetId = baseUiConversation.mCore.targetId
        val items = ArrayList<ImOptionItem>()
        val removeItem = view.context.resources.getString(R.string.conversation_list_dialog_remove)
        val setTopItem = view.context.resources.getString(R.string.conversation_list_dialog_set_top)
        val cancelTopItem =
            view.context.resources.getString(R.string.conversation_list_dialog_cancel_top)
        if (baseUiConversation !is GatheredConversation) {
            if (baseUiConversation.mCore.isTop) {
                items.add(ImOptionItem(cancelTopItem))
            } else {
                items.add(ImOptionItem(setTopItem))
            }
        }
        val actionItemInfo = getItemActionInfo(targetId)
        if (inAppOnlineSwitch()) {
            items.add(ImOptionItem(actionItemInfo.offOnlineTitle, actionItemInfo.offOnlineIcon))
        }
        items.add(ImOptionItem(actionItemInfo.muteTitle, actionItemInfo.muteIcon))
        if (!actionItemInfo.isPenalty) {
            items.add(ImOptionItem(actionItemInfo.invisibleTitle, actionItemInfo.invisibleIcon))
        }
        items.add(ImOptionItem(removeItem))
        OptionsPopupDialog.newInstance(view.context, items).setOptionsPopupDialogListener { which ->
            if (items[which] == ImOptionItem(setTopItem) || items[which] == ImOptionItem(
                    cancelTopItem
                )
            ) {
                // 置顶、取消置顶
                baseUiConversation?.let { conversation ->
                    ChatTopHelper.toTop(conversation.mCore, true) { status ->
                        when (status) {
                            ChatTopHelper.STATUS_SUCCESS -> {
                                ToastUtils.showToast(items[which].optionText)
                            }

                            ChatTopHelper.STATUS_STINT -> {
                                // 提示不能再置顶
                                ChatTopTipDialog.showNotifyItem()
                            }

                            ChatTopHelper.STATUS_ERROR -> {
                            }
                        }
                    }
                }
            } else if (items[which] == ImOptionItem(removeItem)) {
                if (baseUiConversation.mCore.isTop) {
                    // 删除时取消置顶
                    ChatTopHelper.toTop(baseUiConversation.mCore, true) {}
                }
                AppRepository.removeConversation(targetId)
                InitConversationHelper.removeConversion(targetId)
                IMCenter.getInstance().removeConversation(
                    baseUiConversation.mCore.conversationType, targetId, null
                )
                AppRepository.eventTrace(EventKey.K_chat_delete) {
                    "targetId" to targetId
                }
                filterConversationList()
            } else if (items[which] == ImOptionItem(
                    actionItemInfo.offOnlineTitle, actionItemInfo.offOnlineIcon
                )
            ) {
                //在线状态开关
                val successMessage = if (isFlagEnabled(
                        actionItemInfo.chatFlag,
                        ConnectFlagNoOnline
                    )
                ) R.string.turn_on_success else R.string.turn_off_success
                updateChatFlag(
                    targetId, actionItemInfo.chatFlag, ConnectFlagNoOnline, successMessage
                )
            } else if (items[which] == ImOptionItem(
                    actionItemInfo.muteTitle, actionItemInfo.muteIcon
                )
            ) {
                //免打扰开关
                val successMessage = if (isFlagEnabled(
                        actionItemInfo.chatFlag,
                        ConnectFlagMuteMsg
                    )
                ) R.string.mute_un_success else R.string.mute_success
                updateChatFlag(
                    targetId, actionItemInfo.chatFlag, ConnectFlagMuteMsg, successMessage
                )
            } else if (items[which] == ImOptionItem(
                    actionItemInfo.invisibleTitle, actionItemInfo.invisibleIcon
                )
            ) {
                //隐身开关
                if (isFlagEnabled(actionItemInfo.chatFlag, ConnectFlagInvisible)) {
                    updateChatFlag(
                        targetId,
                        actionItemInfo.chatFlag,
                        ConnectFlagInvisible,
                        R.string.visible_success
                    )
                } else {
                    CommonAlertDialog.Builder().setTitle(getString(R.string.visible_off_title))
                        .setContext(getString(R.string.visible_off_content))
                        .setCancel(getString(com.iandroid.allclass.lib_common.R.string.btn_cancel)) {}
                        .setConfirm(getString(R.string.dialog_ok)) {
                            updateChatFlag(
                                targetId,
                                actionItemInfo.chatFlag,
                                ConnectFlagInvisible,
                                R.string.invisible_success
                            )
                        }.create().show(
                            requireActivity().supportFragmentManager,
                            CommonAlertDialog::class.java.name
                        )
                }
            }
        }.show()
        return true
    }

    /**
     * 接口更新开关状态
     */
    private fun updateChatFlag(imUid: String, chatFlag: Long, flag: Long, successMessage: Int) {
        viewModel?.updateChatFlag(imUid, getChatFlag(chatFlag, flag)) {
            val chatFlagUpdate = getChatFlag(chatFlag, flag)
            ToastUtils.showToast(successMessage)
            if (flag == ConnectFlagNoOnline && !it.isNullOrEmpty()) {
                it.forEach { imUid ->
                    refreshSwitchStatus(imUid, chatFlagUpdate)
                }
            } else {
                refreshSwitchStatus(imUid, chatFlagUpdate)
            }
            if (flag == ConnectFlagMuteMsg) {
                IMModule.instance.setConversationNotificationStatus(
                    imUid, isFlagEnabled(chatFlag, flag)
                )
            }
        }
    }

    /**
     * 删除不属于当前任何model的会话
     */
    private fun deleteConversationOnNoModel() {
        if (modelList.isNotEmpty() && !isRemoveConForModel) {
            getConversationValue()?.onEach { conversation ->
                isRemoveConForModel = true
                if (!modelList.any { it.userId == getModelId(conversation.mCore.targetId) }) {
                    RongIMClient.getInstance()
                        .removeConversation(
                            conversation.mCore.conversationType,
                            conversation.mCore.targetId,
                            null
                        )
                }
            }
        }
    }

    /**
     * 融云会话列表数据
     */
    private fun getConversationValue(): List<BaseUiConversation> {
        return InitConversationHelper.mergerConversations(
            mConversationListViewModel?.conversationListLiveData?.value ?: listOf()
        )
    }

    override fun onDestroyView() {
        rlRevitalize?.onDestroy()
        super.onDestroyView()
        RongIM.setConversationListBehaviorListener(null)
    }

    override fun showErrorResponeseUI(msg: String?) {
    }

    override fun showEmptyUI(msg: String?) {
    }

    override fun showNoNetWorkUI(msg: String?) {
    }

    override fun showWeakNetWorkUI(msg: String?) {
    }

    override fun hideErrorUI() {
    }

    override fun provideLayoutResId(): Int {
        return R.layout.fragment_conversationlist
    }

    /**
     * 自定义列表 空数据 view
     */
    private fun setEmptyView() {
        if (emptyView == null) emptyView =
            layoutInflater.inflate(R.layout.layout_conversationlist_empty, containerView, false)
        emptyView?.apply {
            findViewById<ImageView>(R.id.rc_empty_iv).setImageResource(R.drawable.ic_like_empty)
            findViewById<TextView>(R.id.rc_empty_tv).text = getText(R.string.coversation_empty)
        }
        mAdapter?.setEmptyView(emptyView)
    }

    /**
     * 检查不可见mode 有未读数量，显示按钮点击到指定有消息mode
     */
    private fun findFirstUnreadPosition() {
        if (pageModelListView?.adapter == null || pageModelListView.layoutManager == null) {
            return
        }
        val itemCount = pageModelListView.adapter?.itemCount ?: 0
        var firstUnreadPosition = -1
        val layoutManager = pageModelListView.layoutManager
        if (layoutManager is LinearLayoutManager) {
            // 检查屏幕上可见的最后一个项
            val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
            // 确保lastVisiblePosition不大于itemCount
            if (lastVisiblePosition < itemCount) {
                // 从最后一个可见项开始向右遍历
                for (position in lastVisiblePosition until itemCount) {
                    if (position >= 0) {
                        val dataModel =
                            (pageModelListView.adapter as? ChattersModelListAdapter)?.modelList?.get(
                                position
                            )
                        if ((dataModel?.unreadnum
                                ?: 0) > 0 || (modelAdapter!!.onlineNum.containsKey(dataModel?.userId) && modelAdapter!!.onlineNum[dataModel?.userId]!! > 0) || (modelAdapter!!.activeNum.containsKey(
                                dataModel?.userId
                            ) && modelAdapter!!.activeNum[dataModel?.userId]!! > 0)
                        ) {
                            firstUnreadPosition = position
                            break
                        }
                    }
                }
            }
            if (lastVisiblePosition + 1 >= itemCount) {
                rightNewMsgView.show(false)
            } else {
                rightNewMsgView.show(firstUnreadPosition > 0)
            }
        }

        //点击滚动到不可见第一个有消息的position
        rightNewMsgView.setOnClickListener {
            layoutManager?.scrollToPosition(firstUnreadPosition)
            val offsetInPixels = (30 * resources.displayMetrics.density).toInt()
            pageModelListView.post { pageModelListView.smoothScrollBy(offsetInPixels, 0) }
        }
    }

    /**
     * Chatter 活跃时长 计时器
     */
    private fun chatterActiveHours() {
        countDownTimer = object : CountDownTimer(Long.MAX_VALUE, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                elapsedTimeSeconds++
                updateChatterActiveTime()
            }

            override fun onFinish() {

            }
        }
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(ActiveStatusEntity::class) {
            elapsedTimeSeconds = it.totalTime
            if (it.status == Config.activeStatus) { //活跃状态
                activeStatus()
                statusIsActive = true
            } else {  //不活跃状态
                chatterStatus?.text = getString(R.string.chatter_non_active)
                chatterStatus?.textColorResource =
                    com.iandroid.allclass.lib_common.R.color.color_F5222D
                chatterActiveTime?.textColorResource =
                    com.iandroid.allclass.lib_common.R.color.color_F5222D
                updateChatterActiveTime()
                countDownTimer.cancel()
                isTimerRunning = false
                statusIsActive = false
            }
        })
    }

    /**
     * 更新活跃时长
     */
    private fun updateChatterActiveTime() {
        val hours = elapsedTimeSeconds / 3600
        val minutes = (elapsedTimeSeconds % 3600) / 60
        val seconds = elapsedTimeSeconds % 60
        chatterActiveTime?.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    /**
     * 离线状态处理
     */
    private fun offLineStatus(isOffline: Boolean = false) {
        if (isOffline) {
            AppRepository.statusChange(Config.statusNoActive)
            chatterStatus?.text = getString(R.string.chatter_offline)
            chatterStatus?.textColorResource = com.iandroid.allclass.lib_common.R.color.cl_595959
            chatterActiveTime?.textColorResource =
                com.iandroid.allclass.lib_common.R.color.cl_595959
            countDownTimer.cancel()
            isTimerRunning = false
        } else {
            activeStatus()
        }
    }

    /**
     * 活跃状态
     */
    private fun activeStatus() {
        if (!isTimerRunning) {
            chatterStatus?.text = getString(R.string.chatter_active)
            chatterStatus?.textColorResource = com.iandroid.allclass.lib_common.R.color.color_24C004
            chatterActiveTime?.textColorResource =
                com.iandroid.allclass.lib_common.R.color.color_24C004
            countDownTimer.start()
            isTimerRunning = true
            if (statusIsActive) {
                AppRepository.statusChange(Config.statusActive)
            }
        }
    }

    private fun setCurrTag(reset: Boolean = false) {
        if (reset) {
            ImNoticeHelper.currTag(ImNoticeHelper.ACTIVE_OTHER)
            return
        }
        when (conversationChatsTab) {
            ChatsConfig.FlagTabMatch -> {
                ImNoticeHelper.currTag(ImNoticeHelper.ACTIVE_MATCH)
            }

            ChatsConfig.FlagTabAllChats -> {
                ImNoticeHelper.currTag(
                    ImNoticeHelper.ACTIVE_CHATS,
                    modelId = modelAdapter?.curSelUserId.orEmpty()
                )
            }

            else -> {
                ImNoticeHelper.currTag(ImNoticeHelper.ACTIVE_OTHER)
            }
        }
    }

    /**
     * 快速回到底部的按钮处理
     */
    private fun showScrollToTopView() {
        pageListView?.mRecyclerView?.layoutManager?.let { layoutManager ->
            if (mAdapter != null && layoutManager is LinearLayoutManager) {
                (layoutManager as LinearLayoutManager?)?.let {
                    val topVisiblePosition = it.findFirstVisibleItemPosition()
                    // 消息容器最底部没有完全展示出倒数第二条时，显示
                    val isShow =
                        topVisiblePosition >= 3 && topVisiblePosition < mAdapter!!.itemCount
                    if (btnToTop?.isVisible != isShow) {
                        btnToTop.show(isShow)
                    }
                }
            }
        }
    }

    /**
     * 更新联系人标签
     */
    private fun contactTagChange(targetId: String, tagId: Int) {
        // 更新标签数据
        if (targetId.isNotEmpty() && getModelId(targetId).isNotEmpty()) {
            getConversationValue()?.forEach { item ->
                if (item.mCore.targetId == targetId) {
                    // 如果是当前model，刷新UI
                    if (getModelId(targetId) == modelAdapter?.curSelUserId) {
                        if (tagSortById != ChatsConfig.TAG_NO_SEL) {
                            // 已选择tag排序，需要整体刷新
                            filterConversationList()
                        } else {
                            // 刷新单个
                            mAdapter?.data?.forEachIndexed { index, baseUiConversation ->
                                if (baseUiConversation.mCore.targetId == targetId) {
                                    recyclerViewSupport?.updateViewByPosition(index)
                                    return
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCountChanged(count: Int) {
    }

    override fun startRefresh() {
        super.startRefresh()
    }

    /**
     * 获取model列表数据
     */
    private fun getModels() {
        viewModel?.getChatterModelList()
    }

    /**
     * 老用户召回入口
     */
    private fun showRevitalizeIn() {
        if (AppController.showRecallVipBtn() == 1)
            viewModel?.showRevitalizeIn()
        else
            rlRevitalize.show(false)
    }

    /**
     * 初始化定向建联状态入口
     */
    private fun initBonusTipView() {
        viewModel?.bonusTipResult?.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            bonusView.show(it.status > 0)
            bonusView.setTipData(it)
        }
        viewModel?.getBonusTip()
        bonusView.setClickCallback {
            viewModel?.getBonusTip(true)
        }
    }

    /**
     * 展示惩罚记录提示
     */
    private fun initWarningTipView() {
        viewModel?.warningCheckResult?.observe(viewLifecycleOwner) {
            if (it == null) return@observe
            warningCheckView.show(it.records?.isNotEmpty() == true)
            warningCheckView.setData(it)
        }
        viewModel?.warningCheckInfo()
        // 质检变更
        viewModel?.compositeDisposable?.add(
            SimpleRxBus.observe(PenaltyNotifyEntity::class) {
                viewModel?.warningCheckInfo()
            }
        )
    }

    private fun getInitConversations() {
        // 从服务端获取最近会话列表数据
        InitConversationHelper.requestInitConversations { isSuccess ->
            if (isSuccess) {
                initConversationFailed = false
                filterConversationList(false)
            } else {
                initConversationFailed = true
            }
        }
    }

    /**
     * 错误提示更新状态
     */
    private fun updateStatusView(resId: Int, msg: String) {
        chatsStatusPlaceView.show(false)
        chatsStatusRootView.show(true)
        chatsStatusView.show(true)
        statusViewIcon.setImageResource(resId)
        statusViewTitle.text = msg
        statusViewActionButton.setOnClickListener {
            setLoadingUI(true)
            viewModel?.getAiAutoChatId()
            getModels()
            showRevitalizeIn()
            viewModel?.shiftEntranceData()
        }
    }

    private fun setLoadingUI(isLoading: Boolean) {
        if (isLoading) {
            statusViewActionButton.isEnabled = false
            tvRetryLoading.show(true)
            tvRetry.text = getString(R.string.loading_ing)
        } else {
            statusViewActionButton.isEnabled = true
            tvRetryLoading.show(false)
            tvRetry.text = getString(R.string.click_to_retry)
        }
    }

    override fun fragmentVisible(visible: Boolean) {
        super.fragmentVisible(visible)
    }

    /**
     * 埋点
     */
    private fun clickUnlockFilterTrace(tabName: String) {
        CommonRepository.eventTrace(EventKey.im_chat_fliter_tab) {
            "chatterID" to UserController.getUserId()
            "tabName" to tabName
        }
    }

    private fun getMainPage(): MainActivity? = context.castObject<MainActivity>()
}
