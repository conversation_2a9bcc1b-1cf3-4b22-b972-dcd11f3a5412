package com.fascin.chatter.main.task

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.recyclerview.widget.GridLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.TaskCatalogEntity
import com.fascin.chatter.bean.TaskTableItemDescEntity
import com.fascin.chatter.main.task.adapter.TaskDescAdapter
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_basecore.view.recyclerview.GridItemDecoration
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.afterGlobalLayoutMeasured
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_task_desc.btnOk
import kotlinx.android.synthetic.main.dialog_task_desc.rewardLayout
import kotlinx.android.synthetic.main.dialog_task_desc.rlTable
import kotlinx.android.synthetic.main.dialog_task_desc.rvLevelGoal
import kotlinx.android.synthetic.main.dialog_task_desc.rvReward
import kotlinx.android.synthetic.main.dialog_task_desc.svContent
import kotlinx.android.synthetic.main.dialog_task_desc.tvTitle

/**
 * @Desc: 任务说明弹窗
 * @Created: Quan
 * @Date: 2023/11/13
 */
class TaskDescDialog() : BaseDialogFragment() {

    private var type: Int = TYPE_SCROLL
    private var entity: TaskCatalogEntity? = null

    constructor(type: Int, entity: TaskCatalogEntity) : this() {
        this.type = type
        this.entity = entity
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(
            R.layout.dialog_task_desc, container, false
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.887).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        svContent.show(type == TYPE_SCROLL)
        rewardLayout.show(type == TYPE_WRAP)
        entity?.let {
            val size = if (type == TYPE_SCROLL) {
                it.taskLelInfo?.get(0)?.size
            } else {
                2
            }
            val descAdapter = TaskDescAdapter(type == TYPE_WRAP, size!!, ArrayList())
            rlTable.afterGlobalLayoutMeasured {
                descAdapter.setMaxWidth(this.width)
            }
            val gridLayoutManager = GridLayoutManager(context, size)
            val decoration = GridItemDecoration.Builder(context)
                .setColorResource(com.iandroid.allclass.lib_common.R.color.color_d9d9d9)
                .setHorizontalSpan(R.dimen.dp_1)
                .setVerticalSpan(R.dimen.dp_1)
                .setShowLastLine(false)
                .build()
            if (type == TYPE_SCROLL) {
                tvTitle.text = getString(R.string.task_dialog_goal_desc)
                rvLevelGoal.layoutManager = gridLayoutManager
                rvLevelGoal.addItemDecoration(decoration)
                rvLevelGoal.adapter = descAdapter
            } else {
                tvTitle.text = getString(R.string.task_dialog_reward_desc)
                rvReward.layoutManager = gridLayoutManager
                rvReward.addItemDecoration(decoration)
                rvReward.adapter = descAdapter
            }
            descAdapter.setData(getTableViewData())
            btnOk.clickWithTrigger {
                dismiss()
            }
        }
    }

    private fun getTableViewData(): ArrayList<TaskTableItemDescEntity> {
        return ArrayList<TaskTableItemDescEntity>().also { tabContentList ->
            entity?.let {
                if (type == TYPE_SCROLL) {
                    // 当前等级考核对照表数据
                    it.taskLelInfo?.forEachIndexed { index, contents ->
                        for ((j, content) in contents.withIndex()) {
                            tabContentList.add(TaskTableItemDescEntity().also { item ->
                                // 第一行和第一排设置背景色用
                                item.type =
                                    if (index == 0 || j == 0) TaskTableItemDescEntity.TYPE_GRAY
                                    else TaskTableItemDescEntity.TYPE_CONTENT
                                item.content = content
                            })
                        }
                    }
                } else {
                    // 不同等级奖励说明表数据
                    // 添加第一行数据
                    tabContentList.add(TaskTableItemDescEntity().also { item ->
                        item.type = TaskTableItemDescEntity.TYPE_GRAY
                        item.content = "Task level"
                    })
                    tabContentList.add(TaskTableItemDescEntity().also { item ->
                        item.type = TaskTableItemDescEntity.TYPE_GRAY
                        item.content = "Reward"
                    })
                    // 添加第二行往后的数据
                    it.rewardInfo?.forEach { rewardEntity ->
                        tabContentList.add(TaskTableItemDescEntity().also { item ->
                            item.type = TaskTableItemDescEntity.TYPE_GRAY
                            item.content = rewardEntity.name
                        })
                        tabContentList.add(TaskTableItemDescEntity().also { item ->
                            item.type = TaskTableItemDescEntity.TYPE_CONTENT
                            item.content = rewardEntity.value
                        })
                    }
                }
            }
        }

    }

    companion object {
        const val TYPE_SCROLL = 0 // 表格需要滚动
        const val TYPE_WRAP = 1 // 表格不需要滚动
    }
}
