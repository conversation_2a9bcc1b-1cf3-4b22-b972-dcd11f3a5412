package com.fascin.chatter.main.chats

import android.net.Uri
import android.os.Bundle
import android.view.View
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.R
import com.fascin.chatter.bean.ModelUserEntity
import com.fascin.chatter.bean.chat.UserImExtraEntity
import com.fascin.chatter.bean.event.UIContactTagChangeEvent
import com.fascin.chatter.bean.event.UIFindChatInitDataEvent
import com.fascin.chatter.config.TabConfig
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.adapter.ChattersModelListAdapter
import com.fascin.chatter.main.adapter.TabAdapter
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_common.AppController
import com.iandroid.allclass.lib_common.Values
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.beans.HomeTabEntity
import com.iandroid.allclass.lib_common.beans.MixPageEntity
import com.iandroid.allclass.lib_common.event.RefreshOnlineUIEvent
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.exts.castObject
import com.iandroid.allclass.lib_common.utils.exts.objToJsonString
import com.iandroid.allclass.lib_common.utils.exts.toJsonString
import io.rong.imkit.userinfo.RongUserInfoManager
import io.rong.imlib.model.UserInfo
import kotlinx.android.synthetic.main.fragment_vip_contacts.ctsTagSort
import kotlinx.android.synthetic.main.fragment_vip_contacts.findChatVP
import kotlinx.android.synthetic.main.fragment_vip_contacts.rvModel
import kotlinx.android.synthetic.main.fragment_vip_contacts.tabLayout
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * @Desc: 首页 Vip Contacts
 * @Created: Quan
 * @Date: 2024/6/4
 */
class VIPContactsFragment : BaseUiFragment(), TabAdapter.IFragmentTabCreator {

    private var tabAdapter: TabAdapter? = null
    private var modelAdapter: ChattersModelListAdapter? = null
    private var modelList: ArrayList<ModelUserEntity> = ArrayList()
    private var viewModel: ChatsViewModel? = null
    private var chats: HashMap<String, ArrayList<ExploreUserEntity>>? = HashMap()

    // 记录model下会话反生变化时，需要刷新model对应的数据
    private var chatsRefreshFlags: HashMap<String, Boolean> = HashMap()
    private var isFirst = true

    // 优先靠前tag
    private var tagSortById: Int = ChatsConfig.TAG_NO_SEL

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initModelRV()
        initTabAndViewPager()
        setTabViewPagerCallback()
        setViewModel()
    }

    private fun initModelRV() {
        rvModel.layoutManager =
            LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        modelAdapter = ChattersModelListAdapter { userId, reClick ->
            if (!reClick) {
                // 切换tab,筛选数据
                if (chats?.containsKey(userId) == false) {
                    tabAdapter?.fragmentHashList?.forEach {
                        it.value.castObject<FindChatsFragment>()?.also { fragment ->
                            fragment.setPreView()
                        }
                    }
                }
                setFragmentData()
            }
        }
        rvModel.adapter = modelAdapter
        modelAdapter?.isShowUnreadNum(false)
    }

    private fun initTabAndViewPager() {
        findChatVP.offscreenPageLimit = 3
        val tabList = TabConfig.getFindChatsTabList()
        tabAdapter = TabAdapter(tabList, childFragmentManager, lifecycle)
        val autoShowIndex = getDefaultShowTabIndex(tabList)
        findChatVP.adapter = tabAdapter.also { it?.iTabFragmentCreator = this }
        TabLayoutMediator(tabLayout, findChatVP) { tab, position ->
            tab.text = tabList[position].title
        }.attach()

        for (index in tabList.indices) {
            val tab: TabLayout.Tab? = tabLayout.getTabAt(index)
            tab?.setCustomView(R.layout.layout_find_chat_tab_item)
            tab?.customView?.run {
                val tabItemViewTitle = findViewById<TextView>(R.id.tv_tab_name)
                tabItemViewTitle.text = tabList[index].title
                if (index == autoShowIndex) {
                    tabItemViewTitle.isSelected = true
                }
            }
        }

        tabLayout.postDelayed({
            findChatVP?.setCurrentItem(autoShowIndex, false)
        }, 50)
    }

    private fun setTabViewPagerCallback() {
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    val tabTitle = findViewById<TextView>(R.id.tv_tab_name)
                    tabTitle.isSelected = true
                    tabAdapter.castObject<TabAdapter>()?.getFragment(tab.position)
                        ?.castObject<FindChatsFragment>()?.also {
                            it.fragmentVisible(true)
                        }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                tab?.customView?.run {
                    val tabTitle = findViewById<TextView>(R.id.tv_tab_name)
                    tabTitle.isSelected = false
                    tabAdapter.castObject<TabAdapter>()?.getFragment(tab.position)
                        ?.castObject<FindChatsFragment>()?.also {
                            it.fragmentVisible(false)
                        }
                }
            }

            override fun onTabReselected(p0: TabLayout.Tab?) {
            }
        })
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(ChatsViewModel::class.java)
        viewModel?.findChatsLiveData?.observe(this) {
            refreshComplete()
//            refreshUserInfo(it.list!!)
            chats?.put(it.modelId, it.list!!)
//            setModelOnlineNum()
            chatsRefreshFlags[it.modelId] = false
            if (modelAdapter?.curSelUserId == it.modelId)
                setFragmentData()
        }
        viewModel?.findChatsError?.observe(this) {
            refreshComplete()
            if (modelAdapter?.curSelUserId == it)
                setFragmentData(true)
        }
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RefreshOnlineUIEvent::class) {
            // 刷新列表
            tabAdapter?.fragmentHashList?.forEach {
                it.value.castObject<FindChatsFragment>()?.also { fragment ->
                    onlineChange()
                    fragment.updatePageData(chats?.get(modelAdapter?.curSelUserId)!!)
//                    setModelOnlineNum()
                }
            }
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIFindChatInitDataEvent::class) {
            // 子页面下拉刷新回调
            getFindChats(0, true)
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIContactTagChangeEvent::class) {
            // 更新联系人标签
            contactTagChange(it.targetId, it.tagId)
        })

        // 排序规则发生了变化
        ctsTagSort.tagChangeListener(1) {
            tagSortById = it
            tabAdapter?.fragmentHashList?.forEach { fragmentItem ->
                fragmentItem.value.castObject<FindChatsFragment>()?.also { fragment ->
                    // tag排序了，需要整体刷新
                    fragment.tagSortChange(tagSortById)
                    if (chats?.containsKey(modelAdapter?.curSelUserId) == true) {
                        fragment.updatePageData(chats?.get(modelAdapter?.curSelUserId))
                    }
                }
            }
        }

    }

    private fun getFindChats(modelId: Int, getCurrent: Boolean = false) {
        viewModel?.getFindModelChatsList(
            if (getCurrent)
                modelAdapter?.curSelUserId?.toInt()!!
            else
                modelId
        )
    }

    private fun refreshComplete() {
        tabAdapter?.fragmentHashList?.forEach {
            it.value.castObject<FindChatsFragment>()?.also { fragment ->
                fragment.onRefreshComplete()
            }
        }
    }

    private fun setFragmentData(isError: Boolean = false) {
        if (isError) {
            tabAdapter?.fragmentHashList?.forEach {
                it.value.castObject<FindChatsFragment>()?.also { fragment ->
                    fragment.updatePageData(null, true)
                }
            }
        } else if (chats?.containsKey(modelAdapter?.curSelUserId) == true
            && chatsRefreshFlags[modelAdapter?.curSelUserId] == false
        ) {
            // 有数据，并且chat没有发生变化时
            tabAdapter?.fragmentHashList?.forEach {
                it.value.castObject<FindChatsFragment>()?.also { fragment ->
                    fragment.updatePageData(chats?.get(modelAdapter?.curSelUserId)!!)
                }
            }
        } else {
            getFindChats(modelAdapter?.curSelUserId?.toInt()!!)
        }
    }

    /**
     * 默认先展示的tab
     */
    private fun getDefaultShowTabIndex(homeTabList: List<HomeTabEntity>): Int {
        for (index in homeTabList.indices) {
            if (homeTabList[index].default > 0) return index
        }
        return 0
    }

    /**
     * 记录最新离线时间
     */
    private fun onlineChange() {
        chats?.forEach {
            it.value?.forEach { user ->
                val newOffTime = UserOnlineHelper.newOffTime(user.imId)
                if (newOffTime > 0) {
                    user.lastActiveTime = newOffTime
                }
            }
        }
    }

    /**
     * 更新联系人标签
     */
    private fun contactTagChange(targetId: String, tagId: Int) {
        // 更新标签数据
        if (targetId.isNotEmpty() && getModelId(targetId).isNotEmpty()) {
            if (chats?.containsKey(getModelId(targetId)) == true) {
                chats?.get(getModelId(targetId))?.forEach { item ->
                    if (item.imId == targetId) {
                        item.contactTag = tagId
                        // 如果是当前model，刷新UI
                        if (getModelId(targetId) == modelAdapter?.curSelUserId) {
                            tabAdapter?.fragmentHashList?.forEach { fragmentItem ->
                                fragmentItem.value.castObject<FindChatsFragment>()?.also { fragment ->
                                    // tag排序了，需要整体刷新
                                    if (tagSortById != ChatsConfig.TAG_NO_SEL) {
                                        fragment.updatePageData(chats?.get(getModelId(targetId))!!)
                                    } else {
                                        // 刷新单个
                                        fragment.notifyItem(targetId)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onCreateTabFragment(tab: HomeTabEntity): Fragment {
        val fragment = FindChatsFragment().apply {
            arguments = Bundle().also {
                it.putString(
                    Values.intentJsonParam,
                    MixPageEntity(
                        tab.title,
                        tab.url,
                        tab.id
                    ).objToJsonString<MixPageEntity>()
                )
            }
        }
        if (chats?.containsKey(modelAdapter?.curSelUserId) == true) {
            postDelayed({
                fragment.updatePageData(chats?.get(modelAdapter?.curSelUserId)!!)
            }, 150)
        }
        return fragment
    }

    private fun getModelId(imUserId: String): String {
        val targetIdIndex = imUserId.indexOf("_")
        if (targetIdIndex > 0) {
            return imUserId.substring(0, targetIdIndex)
        }
        return ""
    }

    /**
     * 更新数据到缓存
     */
    private fun refreshUserInfo(list: ArrayList<ExploreUserEntity>) {
        if (list.isNullOrEmpty()) return
        if (isFirst) {
            isFirst = false
            lifecycleScope.launch(Dispatchers.IO) {
                for (item in list) {
                    val userInfo = UserInfo(item.imId, item.nickname, Uri.parse(item.avatarUrl))
                    userInfo.extra = UserImExtraEntity().also { extra ->
                        extra.chatLevel = AppController.getChatLvl(item.chatNum)
                        extra.vip = item.vip
                        extra.age = item.age
                        extra.nearby = item.nearby
                        extra.location = item.location
                        extra.latitude = item.latitude
                        extra.longitude = item.longitude
                        extra.lastOnlineDay = item.lastOnlineDay
                        extra.unlockNum = item.unlockNum
                        extra.userChatNum = item.userChatNum
                        extra.newIceBeak = item.newIceBeak
                        extra.chatFlag = item.chatFlag
                        extra.isNewUser = item.isNewUser
                        extra.userFlag = item.userFlag
                        extra.contactTag = item.contactTag
                        extra.lastActiveTime = item.lastActiveTime
                        extra.timeZoneID =
                            item.timeZoneID.ifEmpty { Values.chatDefaultTimeZone }
                    }.toJsonString()
                    RongUserInfoManager.getInstance().refreshUserInfoCacheOnly(userInfo)
                }
            }
        }
    }

    fun setModelList(modelList: ArrayList<ModelUserEntity>, isRemove: Boolean = false) {
        this.modelList.clear()
        this.modelList.addAll(modelList)
        // 首次添加数据时，默认选中第一个，并且会模拟点击，获取当页数据
        modelAdapter?.updateData(this.modelList, isRemove)
    }

    fun setModelRefreshFlag(targetId: String) {
        if (chats?.containsKey(getModelId(targetId)) == true) {
            chatsRefreshFlags[getModelId(targetId)] = true
        }
    }

    override fun attachLayoutId(): Int {
        return R.layout.fragment_vip_contacts
    }
}