package com.fascin.chatter.main.chats

import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.fascin.chatter.R
import com.fascin.chatter.bean.EmptyEntity
import com.fascin.chatter.bean.event.UIContactTagChangeEvent
import com.fascin.chatter.bean.event.VIpContactRefreshEvent
import com.fascin.chatter.im.UserOnlineHelper
import com.fascin.chatter.main.IRvItemAction
import com.iandroid.allclass.lib_basecore.base.BaseUiFragment
import com.iandroid.allclass.lib_basecore.view.recyclerview.BaseRvItemInfo
import com.iandroid.allclass.lib_basecore.view.recyclerview.PullBaseView
import com.iandroid.allclass.lib_basecore.view.recyclerview.RecyclerViewSupport
import com.iandroid.allclass.lib_common.AppViewType
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.beans.ExploreUserEntity
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.event.RefreshOnlineUIEvent
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.route.ActionType
import com.iandroid.allclass.lib_common.route.routeAction
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import io.rong.imkit.RongIM
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import kotlinx.android.synthetic.main.fragment_vip_conflate.ctsVcTagSort
import kotlinx.android.synthetic.main.fragment_vip_conflate.ivVcUnlockFilter
import kotlinx.android.synthetic.main.fragment_vip_conflate.llSearch
import kotlinx.android.synthetic.main.fragment_vip_conflate.llVcUnlockFilter
import kotlinx.android.synthetic.main.fragment_vip_conflate.rvVipConflate

/**
 * @Desc: 首页 Vip Contacts 不区分model
 * @Created: Quan
 * @Date: 2024/6/4
 */
class VIPConflateFragment : BaseUiFragment(), IRvItemAction {

    private var viewModel: ChatsViewModel? = null
    private var chats: ArrayList<ExploreUserEntity> = ArrayList()
    private var recyclerViewSupport: RecyclerViewSupport? = null
    private var refreshing = false

    // 优先靠前tag
    private var tagSortById: Int = ChatsConfig.TAG_NO_SEL

    // 是否开了解锁数排序
    private var unlockFilterOpen = false

    override fun attachLayoutId(): Int {
        return R.layout.fragment_vip_conflate
    }

    override fun initView(view: View?) {
        super.initView(view)
        initRV()
        setPreView()
        setViewModel()
        getFindChats()
    }

    private fun initRV() {
        recyclerViewSupport = RecyclerViewSupport(childFragmentManager, rvVipConflate, null).also {
            it.setCanPullDown(true)
            it.setCanPullUp(false)
            it.recyclerView.setOnRefreshListener(object : PullBaseView.OnRefreshListener {
                override fun onHeaderRefresh() {
                    getFindChats()
                }

                override fun onFooterRefresh() {
                }
            })
        }
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(ChatsViewModel::class.java)
        ChatsConfig.shareViewModel = viewModel
        viewModel?.vipConflateLiveData?.observe(this) {
            refreshComplete()
            it?.let { datas ->
                chats = datas
            }
            setPageData()
            SimpleRxBus.post(VIpContactRefreshEvent())
        }
        viewModel?.vipConflateError?.observe(this) {
            refreshComplete()
            addErrorView()
        }
        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(RefreshOnlineUIEvent::class) {
            // 刷新列表
            onlineChange()
            recyclerViewSupport?.updateView()
        })

        viewModel?.compositeDisposable?.add(SimpleRxBus.observe(UIContactTagChangeEvent::class) {
            // 更新联系人标签
            contactTagChange(it.targetId, it.tagId)
        })

        llSearch.clickWithTrigger {
            context.routeAction(ActionType.actionVipSearch)
        }

        // 排序规则发生了变化
        ctsVcTagSort.tagChangeListener(1) {
            tagSortById = it
            setPageData()
        }

        // 按解锁数量排序
        llVcUnlockFilter.clickWithTrigger(300) {
            unlockFilterOpen = !unlockFilterOpen
            ivVcUnlockFilter.setImageResource(
                if (unlockFilterOpen) R.drawable.ic_chat_unlock_filter
                else R.drawable.ic_chat_unlock_unfilter
            )
            setPageData()
            clickUnlockFilterTrace()
        }

    }

    private fun getFindChats() {
        if (refreshing) return
        refreshing = true
        viewModel?.getVIPConflateList()
    }

    private fun refreshComplete() {
        refreshing = false
        recyclerViewSupport?.onHeaderRefreshComplete()
        recyclerViewSupport?.onFooterRefreshComplete()
    }

    private fun setPreView() {
        setData(ArrayList<BaseRvItemInfo?>().also { list ->
            repeat(7) {
                list.add(BaseRvItemInfo(Any(), AppViewType.findChatsPlaceItemView, this))
            }
        })
    }

    private fun setPageData() {
        if (chats.isNotEmpty())
            sortData(chats)
        setData(
            ArrayList<BaseRvItemInfo?>().also {
                chats.forEach { entity ->
                    it.add(BaseRvItemInfo(entity, AppViewType.findChatsItemView, this))
                }
            }, true
        )
    }

    private fun setData(itemTemp: ArrayList<BaseRvItemInfo?>, needClear: Boolean = true) {
        if (itemTemp.isNotEmpty()) updateData(itemTemp, needClear)
        else if (needClear) addEmptyView()
    }

    private fun updateData(itemTemp: ArrayList<BaseRvItemInfo?>?, clearData: Boolean) {
        if (recyclerViewSupport != null) recyclerViewSupport?.updateData(itemTemp, clearData)
    }

    private fun addEmptyView() {
        val emptyEntity = EmptyEntity().also {
            it.content = getString(R.string.page_nodata_find_chat)
            it.icRes = R.mipmap.ic_msg_setting_nodata
        }

        emptyEntity.also { data ->
            updateData(ArrayList<BaseRvItemInfo?>().also {
                it.add(BaseRvItemInfo(data, AppViewType.comEmptyView, this))
            }, true)
        }
    }

    private fun addErrorView() {
        updateData(ArrayList<BaseRvItemInfo?>().also {
            it.add(BaseRvItemInfo(Any(), AppViewType.exceptionView, this))
        }, true)
    }

    /**
     * 记录最新离线时间
     */
    private fun onlineChange() {
        chats.forEach { user ->
            val newOffTime = UserOnlineHelper.newOffTime(user.imId)
            if (newOffTime > 0) {
                user.lastActiveTime = newOffTime
            }
        }
    }

    /**
     * 更新联系人标签
     */
    private fun contactTagChange(targetId: String, tagId: Int) {
        // 更新标签数据
        if (targetId.isNotEmpty()) {
            chats.forEach { item ->
                if (item.imId == targetId) {
                    item.contactTag = tagId
                    // tag排序了，需要整体刷新
                    if (tagSortById != ChatsConfig.TAG_NO_SEL) {
                        setPageData()
                        return
                    } else {
                        // 刷新单个
                        recyclerViewSupport?.infos?.forEachIndexed { index, itemInfo ->
                            if (itemInfo.data is ExploreUserEntity) {
                                (itemInfo.data as ExploreUserEntity).also { item ->
                                    if (item.imId == targetId) {
                                        item.contactTag = tagId
                                        recyclerViewSupport?.updateViewByPosition(index)
                                        return
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 有会话产生新消息时，更新
     */
    fun setChatRefreshFlag(targetId: String) {
        if (chats.any { it.imId == targetId }) {
            RongIM.getInstance().getConversation(Conversation.ConversationType.PRIVATE, targetId, object :
                RongIMClient.ResultCallback<Conversation>() {
                override fun onSuccess(conversation: Conversation?) {
                    if (conversation == null) return
                    chats.find { it.imId == targetId }?.also { item ->
                        var flag = item.msgFlag
                        if (conversation.unreadMessageCount > 0) {
                            flag = ChatsConfig.FlagNewMsg
                        } else if (conversation.latestMessageDirection == Message.MessageDirection.SEND) {
                            flag = ChatsConfig.FlagDelivered
                        } else if (conversation.latestMessageDirection == Message.MessageDirection.RECEIVE) {
                            flag = ChatsConfig.FlagReceived
                        }
                        item.msgFlag = flag
                        item.lastChatTime = conversation.sentTime / 1000 // lastChatTime与sentTime单位不一样
                        // 刷新单个
                        recyclerViewSupport?.infos?.forEachIndexed { index, itemInfo ->
                            if (itemInfo.data is ExploreUserEntity) {
                                (itemInfo.data as ExploreUserEntity).also { rvitem ->
                                    if (rvitem.imId == targetId) {
                                        rvitem.msgFlag = flag
                                        rvitem.lastChatTime = conversation.sentTime / 1000
                                        recyclerViewSupport?.updateViewByPosition(index)
                                        return
                                    }
                                }
                            }
                        }
                    }
                }

                override fun onError(e: RongIMClient.ErrorCode?) {

                }
            })
        }
    }

    fun hasOnline(): Boolean {
        chats.forEach {
            if (UserOnlineHelper.isOnline(it.imId)) {
                return true
            }
        }
        return false
    }

    /**
     * 排序
     */
    private fun sortData(chatList: ArrayList<ExploreUserEntity>?) {
        chatList?.sortWith(compareByDescending<ExploreUserEntity> {
            //  如果开启了按tag筛选，将对应tag的排在前面
            tagSortById != ChatsConfig.TAG_NO_SEL && it.contactTag == tagSortById
        }.thenByDescending {
            // 先按在线排
            UserOnlineHelper.getOnline(it.imId)
        }.thenByDescending {
            // 再按活跃状态排
            UserOnlineHelper.isActive(it.imId, it.lastActiveTime)
        }.thenByDescending {
            // 开启了按解锁数排序时，按model解锁数排
            val unlockNum = if (unlockFilterOpen) it.unlockNum else 0
            unlockNum
        }.thenByDescending {
            // 按总解锁数排
            it.platformUnlockNum
        }.thenByDescending {
            // 按最后聊天时间降序排
            it.lastChatTime
        })
    }


    /**
     * 埋点
     */
    private fun clickUnlockFilterTrace() {
        CommonRepository.eventTrace(EventKey.im_chat_unlock_sort) {
            "chatterID" to UserController.getUserId()
            "from" to 1
        }
    }

    override fun onRefresh() {
        getFindChats()
    }
}