package com.fascin.chatter.main.view

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.fascin.chatter.AppModule
import com.fascin.chatter.R
import com.fascin.chatter.bean.event.UIRevitalizeSendEvent
import com.fascin.chatter.component.views.SUButtonStatus
import com.fascin.chatter.main.MainViewModel
import com.fascin.chatter.main.adapter.RevitalizeAdapter
import com.iandroid.allclass.lib_basecore.utils.DeviceUtils
import com.iandroid.allclass.lib_common.UserController
import com.iandroid.allclass.lib_common.base.BaseDialogFragment
import com.iandroid.allclass.lib_common.event.EventKey
import com.iandroid.allclass.lib_common.repository.CommonRepository
import com.iandroid.allclass.lib_common.rxbus.SimpleRxBus
import com.iandroid.allclass.lib_common.utils.ToastUtils
import com.iandroid.allclass.lib_common.utils.clickWithTrigger
import com.iandroid.allclass.lib_common.utils.exts.show
import kotlinx.android.synthetic.main.dialog_revitslize.btnTryAgain
import kotlinx.android.synthetic.main.dialog_revitslize.editRevitalize
import kotlinx.android.synthetic.main.dialog_revitslize.flPlace
import kotlinx.android.synthetic.main.dialog_revitslize.ivSend
import kotlinx.android.synthetic.main.dialog_revitslize.llEdit
import kotlinx.android.synthetic.main.dialog_revitslize.llEmpty
import kotlinx.android.synthetic.main.dialog_revitslize.llError
import kotlinx.android.synthetic.main.dialog_revitslize.rvRevitalize
import kotlinx.android.synthetic.main.dialog_tiral_goals.ivClose

/**
 * VIP老用户召回弹窗
 */
class RevitalizeDialog() : BaseDialogFragment() {

    private var viewModel: MainViewModel? = null
    private var adapter: RevitalizeAdapter? = null

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_revitslize, container, false)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NO_FRAME, com.iandroid.allclass.lib_common.R.style.com_anim_dialog)
    }

    override fun onStart() {
        super.onStart()
        setCenterPopupAttr(
            (DeviceUtils.getScreenWidth(context) * 0.86).toInt(),
            WindowManager.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        isCancelable = false
        AppModule.userActive()
        setPreview()
        setRv()
        setViewModel()
        setListener()
    }

    private fun setPreview() {
        flPlace.show(true)
        rvRevitalize.show(false)
        llError.show(false)
        llEmpty.show(false)
        llEdit.show(false)
    }

    private fun setRv() {
        ivSend.isEnabled = false
        rvRevitalize.layoutManager = LinearLayoutManager(context)
        adapter = RevitalizeAdapter()
        rvRevitalize.adapter = adapter
    }

    private fun setViewModel() {
        viewModel = ViewModelProvider(this).get(MainViewModel::class.java)
        viewModel?.getRevitalizeList()
        viewModel?.revitalizeListResult?.observe(this) {
            val total = it?.list?.size ?: 0
            traceTotal(total)
            if (it.list.isNotEmpty()) {
                SimpleRxBus.post(UIRevitalizeSendEvent())
                rvRevitalize.show(true)
                llError.show(false)
                flPlace.show(false)
                llEmpty.show(false)
                llEdit.show(true)
                adapter?.updateData(it.list)
            } else {
                rvRevitalize.show(false)
                llError.show(false)
                flPlace.show(false)
                llEmpty.show(true)
                llEdit.show(false)
            }
        }

        viewModel?.revitalizeListError?.observe(this) {
            rvRevitalize.show(false)
            llError.show(true)
            llEmpty.show(false)
            flPlace.show(false)
            llEdit.show(false)
            btnTryAgain.setButtonStatus(SUButtonStatus.Activated)
        }

        viewModel?.revitalizeSendResult?.observe(this) {
            ToastUtils.showToast("Success")
            dismissAllowingStateLoss()
        }

        viewModel?.revitalizeSendError?.observe(this) {

        }
    }

    private fun setListener() {
        ivClose.clickWithTrigger {
            dismissAllowingStateLoss()
        }

        btnTryAgain.clickWithTrigger {
            setPreview()
            viewModel?.getRevitalizeList()
        }

        ivSend.clickWithTrigger {
            if (adapter?.selectIds?.isNotEmpty() == true && editRevitalize.text.isNotEmpty()) {
                traceSelect(adapter?.itemCount ?: 0, adapter?.selectIds?.size ?: 0)
                viewModel?.revitalizeSendMsg(
                    adapter?.selectIds?.joinToString(separator = ",").orEmpty(),
                    editRevitalize.text.toString().trim()
                )
            }
        }

        editRevitalize.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                ivSend.isEnabled = s?.isNotEmpty() == true
            }

            override fun afterTextChanged(s: Editable?) {
            }
        })

        editRevitalize.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                if (editRevitalize.text.isNotEmpty()) {
                    ivSend.isEnabled = true
                }
            } else {
                if (editRevitalize.text.isNullOrEmpty()) {
                    ivSend.isEnabled = false
                }
            }
        }
    }

    /**
     * 跟踪选择数量
     */
    private fun traceTotal(total: Int) {
        CommonRepository.eventTrace(EventKey.recall_user_exposure) {
            "chatter" to UserController.getUserId()
            "total" to total
        }
    }

    /**
     * 跟踪选择数量
     */
    private fun traceSelect(total: Int, selectCount: Int) {
        CommonRepository.eventTrace(EventKey.recall_user_sent_click) {
            "chatter" to UserController.getUserId()
            "total" to total
            "select_count" to selectCount
        }
    }
}