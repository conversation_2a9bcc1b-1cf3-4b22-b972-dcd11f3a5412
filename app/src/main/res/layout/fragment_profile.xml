<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@mipmap/bg_traffic_top_gradient"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true">

        <com.iandroid.allclass.lib_basecore.view.FontTextView
            android:id="@+id/profile_title_bar"
            style="@style/boldItalic_24_75"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:gravity="center"
            android:text="@string/profile_me"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/profile_title_bar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="30dp">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/chatterInfo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingHorizontal="16dp"
                    android:paddingTop="16dp">

                    <com.iandroid.allclass.lib_basecore.view.FontTextView
                        android:id="@+id/nickName"
                        style="@style/medium_16_75"
                        android:layout_width="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/cl_595959"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="Savannah Nguyen" />

                    <com.iandroid.allclass.lib_basecore.view.FontTextView
                        android:id="@+id/userID"
                        style="@style/bold_22_75"
                        android:layout_marginTop="4dp"
                        android:layout_marginStart="8dp"
                        android:textColor="@color/black"
                        app:layout_constraintStart_toEndOf="@id/tvFraction"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="ID:787821" />

                    <net.csdn.roundview.RoundTextView
                        android:id="@+id/tvFraction"
                        style="@style/semiBold_13_75"
                        android:layout_width="23dp"
                        android:layout_height="23dp"
                        android:background="@color/cl_9370DB"
                        android:gravity="center"
                        android:textColor="@color/white"
                        android:visibility="gone"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/userID"
                        app:layout_constraintBottom_toBottomOf="@id/userID"
                        app:rRadius="12dp"
                        tools:text="90"
                        tools:visibility="visible" />

                    <com.fascin.chatter.component.views.WorkShiftEntranceView
                        android:id="@+id/shiftEntranceView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        app:layout_constraintStart_toEndOf="@id/userID"
                        app:layout_constraintTop_toTopOf="@id/userID"
                        app:layout_constraintBottom_toBottomOf="@id/userID"/>

                    <!-- mcp 注释
                    <androidx.constraintlayout.utils.widget.ImageFilterView
                        android:id="@+id/chatterMpc"
                        android:layout_width="180dp"
                        android:layout_height="40dp"
                        android:src="@drawable/ic_profile_mpc"
                        android:visibility="visible"
                        app:layout_constraintBottom_toBottomOf="@id/userID"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/nickName" />

                    <net.csdn.roundview.RoundView
                        android:id="@+id/mpcHasNew"
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:layout_marginEnd="4dp"
                        android:background="@color/color_F74E57"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@+id/chatterMpc"
                        app:layout_constraintTop_toTopOf="@+id/chatterMpc"
                        app:rRadius="5dp"
                        app:rStrokeColor="@color/white"
                        app:rStrokeWidth="1dp"
                        tools:visibility="visible" />-->

                    <androidx.constraintlayout.utils.widget.ImageFilterView
                        android:id="@+id/topCustomService"
                        android:layout_width="34dp"
                        android:layout_height="27dp"
                        android:src="@mipmap/ic_profile_top_customservice"
                        app:layout_constraintTop_toTopOf="@id/userID"
                        app:layout_constraintBottom_toBottomOf="@id/userID"
                        app:layout_constraintEnd_toEndOf="parent"/>

                    <net.csdn.roundview.RoundView
                        android:id="@+id/mpcHasNew"
                        android:layout_width="10dp"
                        android:layout_height="10dp"
                        android:layout_marginEnd="4dp"
                        android:background="@color/color_F74E57"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="@+id/topCustomService"
                        app:layout_constraintTop_toTopOf="@+id/topCustomService"
                        app:rRadius="5dp"
                        app:rStrokeColor="@color/white"
                        app:rStrokeWidth="1dp"
                        tools:visibility="gone" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:id="@+id/flRv"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    app:rRadius="8dp"
                    app:rStrokeColor="@color/color_d9d9d9"
                    app:rStrokeWidth="1px"
                    tools:visibility="visible">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivAddModel"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginEnd="4dp"
                        android:layout_marginTop="12dp"
                        android:visibility="gone"
                        tools:visibility="visible"
                        android:src="@drawable/ic_me_add_model"/>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/modelList"
                        style="@style/common_noscrolllistview"
                        android:layout_height="120dp"
                        android:overScrollMode="never"
                        tools:itemCount="1"
                        tools:listitem="@layout/itemview_me_model" />

                </androidx.appcompat.widget.LinearLayoutCompat>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clWithdraw"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/bg_withdraw_shape">

                    <net.csdn.roundview.RoundView
                        android:id="@+id/rv_withdraw_bg"
                        android:layout_width="match_parent"
                        android:layout_height="103dp"
                        android:layout_margin="1px"
                        android:background="@drawable/bg_gradient_d6c3fd_9470dc"
                        app:layout_constraintTop_toTopOf="parent"
                        app:rTopRadius="12dp" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/icBalanceIcon"
                        android:layout_width="94dp"
                        android:layout_height="75dp"
                        android:src="@mipmap/ic_profile_balance"
                        android:layout_marginEnd="12dp"
                        app:layout_constraintTop_toTopOf="@id/rv_withdraw_bg"
                        app:layout_constraintEnd_toEndOf="@id/rv_withdraw_bg"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/medium_17_75"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="16dp"
                        android:text="@string/withdraw_current_balance"
                        android:textColor="@color/cl_000000_80"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="@id/rv_withdraw_bg" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvCurrentBalance"
                        style="@style/bold_34_75"
                        android:layout_width="0dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="8dp"
                        android:layout_marginBottom="16dp"
                        android:autoSizeMaxTextSize="34sp"
                        android:autoSizeMinTextSize="26sp"
                        android:autoSizeStepGranularity="1sp"
                        android:autoSizeTextType="uniform"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="0"
                        android:textColor="@color/black"
                        app:layout_constraintBottom_toBottomOf="@id/rv_withdraw_bg"
                        app:layout_constraintEnd_toStartOf="@id/btnWithDraw"
                        app:layout_constraintStart_toStartOf="parent" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/btnWithDraw"
                        android:layout_width="98dp"
                        android:layout_height="26dp"
                        android:layout_marginEnd="16dp"
                        android:src="@mipmap/ic_withdraw_btn"
                        android:scaleType="fitXY"
                        android:layout_marginBottom="18dp"
                        app:layout_constraintBottom_toBottomOf="@id/rv_withdraw_bg"
                        app:layout_constraintEnd_toEndOf="parent" />


                    <TextView
                        android:id="@+id/tvWithdrawNotice"
                        android:text="@string/profile_home_withdraw_notice"
                        android:fontFamily="@font/gilroy_bold"
                        android:textSize="10sp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="3dp"
                        app:layout_constraintStart_toStartOf="@id/tvCurrentBalance"
                        app:layout_constraintBottom_toBottomOf="@id/rv_withdraw_bg"/>

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:id="@+id/ll_balance_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="12dp"
                        app:layout_constraintTop_toBottomOf="@id/rv_withdraw_bg">

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvTotalEarned"
                                style="@style/semiBold_17_75"
                                android:text="0"
                                android:textColor="@color/black" />

                            <androidx.appcompat.widget.AppCompatTextView
                                style="@style/semiBold_12_75"
                                android:layout_marginTop="8dp"
                                android:text="@string/withdraw_total_earned"
                                android:textColor="@color/cr_000000_50" />
                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvPending"
                                style="@style/semiBold_17_75"
                                android:text="0"
                                android:textColor="@color/black" />

                            <androidx.appcompat.widget.AppCompatTextView
                                style="@style/semiBold_12_75"
                                android:layout_marginTop="8dp"
                                android:text="@string/withdraw_pending"
                                android:textColor="@color/cr_000000_50" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                        <androidx.appcompat.widget.LinearLayoutCompat
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvPaid"
                                style="@style/semiBold_17_75"
                                android:text="0"
                                android:textColor="@color/black" />

                            <androidx.appcompat.widget.AppCompatTextView
                                style="@style/semiBold_12_75"
                                android:layout_marginTop="8dp"
                                android:text="@string/withdraw_paid"
                                android:textColor="@color/cr_000000_50" />

                        </androidx.appcompat.widget.LinearLayoutCompat>

                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <View
                        android:id="@+id/view_withdraw"
                        android:layout_width="match_parent"
                        android:layout_height="1px"
                        android:layout_marginHorizontal="12dp"
                        android:background="@color/color_d9d9d9"
                        app:layout_constraintTop_toBottomOf="@id/ll_balance_detail" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvViewAllDetails"
                        style="@style/semiBold_13_75"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:drawableEnd="@mipmap/ic_arrow_right_black"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="20dp"
                        android:text="@string/withdraw_view_all_details"
                        android:textColor="@color/cl_595959"
                        app:layout_constraintTop_toBottomOf="@id/view_withdraw" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="8dp"
                    android:padding="8dp">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/parentCourse"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:maxWidth="116dp">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:src="@mipmap/ic_profile_home_course_bg"
                            android:scaleType="fitXY"/>

                        <ImageView
                            android:layout_width="46dp"
                            android:layout_height="46dp"
                            android:src="@mipmap/ic_profile_home_course"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"/>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="8dp"
                        android:layout_height="wrap_content"/>

                    <!--目前不开放mpc-->
                    <ImageView
                        android:id="@+id/parentMPC"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:maxWidth="116dp"
                        android:src="@mipmap/ic_profile_home_mpc"
                        android:visibility="gone"/>

                    <View
                        android:layout_width="8dp"
                        android:layout_height="wrap_content"
                        android:visibility="gone"/>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/parentPenalties"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1"
                        android:maxWidth="116dp">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:src="@mipmap/ic_profile_home_penalties_bg"
                            android:scaleType="fitXY"/>

                        <ImageView
                            android:layout_width="42dp"
                            android:layout_height="45dp"
                            android:src="@mipmap/ic_profile_home_penalties"
                            android:layout_marginStart="5dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"/>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </androidx.appcompat.widget.LinearLayoutCompat>

                <TextView
                    style="@style/bold_14_75"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="20dp"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="Settings"
                    android:textColor="@color/cl_262626" />

                <net.csdn.roundview.RoundLinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="8dp"
                    android:orientation="vertical"
                    android:paddingHorizontal="12dp"
                    app:rRadius="8dp"
                    app:rStrokeColor="@color/color_d9d9d9"
                    app:rStrokeWidth="1px">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/settingCService"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_customer_service"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="Customer service"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <net.csdn.roundview.RoundTextView
                            android:id="@+id/tvCServiceNum"
                            style="@style/regular_12_75"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="30dp"
                            android:background="@color/color_FF4D4F"
                            android:gravity="center"
                            android:minWidth="20dp"
                            android:paddingHorizontal="4dp"
                            android:textColor="@color/white"
                            android:visibility="gone"
                            app:rRadius="10dp"
                            tools:text="66"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_alignParentBottom="true"
                            android:background="@color/color_d9d9d9" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/settingPenalties"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_penalties"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="@string/penalties"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <net.csdn.roundview.RoundTextView
                            android:id="@+id/tvPenaltiesNum"
                            style="@style/regular_12_75"
                            android:layout_width="wrap_content"
                            android:layout_height="20dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="30dp"
                            android:background="@color/color_FF4D4F"
                            android:gravity="center"
                            android:minWidth="20dp"
                            android:paddingHorizontal="4dp"
                            android:textColor="@color/white"
                            android:visibility="gone"
                            app:rRadius="10dp"
                            tools:text="66"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_alignParentBottom="true"
                            android:background="@color/color_d9d9d9" />
                    </RelativeLayout>

                    <!--绑定/邀请好友-->
                    <RelativeLayout
                        android:id="@+id/rlInvite"
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:visibility="gone">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/settingInvite"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_invite"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="@string/invite_friends"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/tvInviteNew"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="36dp"
                            android:gravity="center"
                            android:src="@mipmap/ic_invite_new"
                            android:visibility="visible"
                            tools:visibility="visible" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_alignParentBottom="true"
                            android:background="@color/color_d9d9d9" />
                    </RelativeLayout>
                    <!--feature guides-->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp"
                        android:visibility="gone">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/settingFeature"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_feature"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="@string/text_feature_guide"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_alignParentBottom="true"
                            android:background="@color/color_d9d9d9" />
                    </RelativeLayout>

                    <LinearLayout
                        android:id="@+id/trafficRoot"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!--Match流量开关-->
                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/trafficSetting"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_traffic"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingHorizontal="4dp"
                            android:text="@string/profile_traffic"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:background="@color/color_d9d9d9" />
                    </LinearLayout>

                    <com.iandroid.allclass.lib_basecore.view.FontTextView
                        android:id="@+id/messageSetting"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:drawableStart="@mipmap/ic_setting_msg"
                        android:drawableEnd="@mipmap/ic_arrow_right_black"
                        android:drawablePadding="8dp"
                        android:fontFamily="@font/gilroy_regular"
                        android:gravity="start|center_vertical"
                        android:paddingStart="4dp"
                        android:paddingEnd="4dp"
                        android:text="Message settings"
                        android:textColor="@color/cl_000000_75"
                        android:textSize="16sp"
                        android:visibility="gone" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/receivingAccount"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_msg"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="@string/payroll_account"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:background="@color/color_d9d9d9" />
                    </LinearLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/settingNotify"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_notice"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="Notification settings"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/isOnlineNoticeOff"
                            style="@style/regular_16_75"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="36dp"
                            android:text="Off"
                            android:textColor="@color/cl_595959"
                            android:visibility="gone" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_alignParentBottom="true"
                            android:background="@color/color_d9d9d9" />
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/clearCacheBtn"
                            android:layout_width="match_parent"
                            android:layout_height="56dp"
                            android:drawableStart="@mipmap/ic_setting_cache"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="Clear Cache"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvCacheSize"
                            style="@style/regular_16_75"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="36dp"
                            android:textColor="@color/cl_595959"
                            tools:text="108MB" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1px"
                            android:layout_alignParentBottom="true"
                            android:background="@color/color_d9d9d9" />

                    </RelativeLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="56dp">

                        <com.iandroid.allclass.lib_basecore.view.FontTextView
                            android:id="@+id/settingCheckUpdate"
                            style="@style/match_match"
                            android:layout_height="match_parent"
                            android:drawableStart="@mipmap/ic_setting_version"
                            android:drawableEnd="@mipmap/ic_arrow_right_black"
                            android:drawablePadding="8dp"
                            android:fontFamily="@font/gilroy_regular"
                            android:gravity="start|center_vertical"
                            android:paddingStart="4dp"
                            android:paddingEnd="4dp"
                            android:text="Version"
                            android:textColor="@color/cl_262626"
                            android:textSize="16sp"
                            app:fontFamily="@font/gilroy_medium"
                            app:layout_constraintStart_toStartOf="parent" />

                        <View
                            android:id="@+id/viewMargin"
                            android:layout_width="30dp"
                            android:layout_height="match_parent"
                            app:layout_constraintEnd_toEndOf="parent" />

                        <ImageView
                            android:id="@+id/updateNew"
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:src="@drawable/rc_unread_count_bg_normal"
                            android:visibility="gone"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/viewMargin"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:visibility="visible" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvVersionCode"
                            style="@style/regular_16_75"
                            android:layout_marginEnd="8dp"
                            android:textColor="@color/cl_595959"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/updateNew"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="V1.0" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </net.csdn.roundview.RoundLinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>