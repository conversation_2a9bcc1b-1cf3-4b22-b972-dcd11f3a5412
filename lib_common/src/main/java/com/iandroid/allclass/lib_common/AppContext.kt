package com.iandroid.allclass.lib_common

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Process
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import com.iandroid.allclass.lib_basecore.ActivityStack
import com.iandroid.allclass.lib_common.core.IUploadInterface
import com.iandroid.allclass.lib_common.route.IRouteParser
import com.iandroid.allclass.lib_common.utils.exts.castObject
import java.lang.ref.WeakReference

/**
 * Created by david on 2020/8/27.
 */
object AppContext : Application.ActivityLifecycleCallbacks {
    lateinit var context: Context
    lateinit var iRouteParser: IRouteParser
    lateinit var iUploadHandle: IUploadInterface
    lateinit var versionName: String
    lateinit var clientName: String
    var versionCode: Int = 0

    var weakRefTopActivityContext: WeakReference<Activity>? = null


    private val events = mutableMapOf<LifecycleEvent, MutableSet<(Context) -> Unit>>()

    fun register(
        event: LifecycleEvent,
        content: (Context) -> Unit
    ) {
        events[event] = (events[event] ?: mutableSetOf()).apply { add { content(it) } }
    }


    override fun onActivityCreated(activity: Activity, p1: Bundle?) {
        updateTopActivity(activity, false)
        ActivityStack.getInstance().add(activity)

        events[LifecycleEvent.CREATE]?.forEach { it(activity) }
    }

    override fun onActivityStarted(activity: Activity) {
        events[LifecycleEvent.START]?.forEach { it(activity) }
    }

    override fun onActivityResumed(activity: Activity) {
        updateTopActivity(activity, false)
        ActivityStack.getInstance().topActivity = activity

        events[LifecycleEvent.RESUME]?.forEach { it(activity) }
    }

    override fun onActivityPaused(activity: Activity) {
        events[LifecycleEvent.PAUSE]?.forEach { it(activity) }
    }

    override fun onActivityStopped(activity: Activity) {
        events[LifecycleEvent.STOP]?.forEach { it(activity) }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        ActivityStack.getInstance().saveInstance(activity, outState)
    }

    override fun onActivityDestroyed(activity: Activity) {
        updateTopActivity(activity, true)
        ActivityStack.getInstance().remove(activity)

        events[LifecycleEvent.DESTROY]?.forEach { it(activity) }
    }

    private fun updateTopActivity(activity: Activity, isdestory: Boolean) {
        if (isdestory) { //destory
            weakRefTopActivityContext?.takeIf { it.get() == activity }?.run {
                weakRefTopActivityContext = null
            }
        } else { //create or onresume
            weakRefTopActivityContext = WeakReference(activity)
        }
    }

    fun getActivitySize(): Int {
        return ActivityStack.getInstance().size
    }

    /**
     * 获取目前存活的顶层activity
     *
     * @return
     */
    fun getTopActivity(): Activity? {
        return weakRefTopActivityContext?.get()?.takeIf { !it.isDestroyed }?.run {
            this
        }
    }

    fun getTopFragmentActivity(): FragmentActivity? {
        return weakRefTopActivityContext?.get()?.takeIf { !it.isDestroyed }
            ?.castObject<FragmentActivity>().run {
                this
            }
    }

    fun getActivity(cls: Class<*>): Activity? {
        return ActivityStack.getInstance().getActivity(cls)
    }

    fun finishAllActivity() {
        return ActivityStack.getInstance().finishAllActivity()
    }

    fun finishActivity(clazzStr: String) {
        return ActivityStack.getInstance().finishActivity(clazzStr)
    }

    fun finishActivity(clazz: Class<out Activity>) {
        ActivityStack.getInstance().finishActivity(clazz)
    }

    fun finishActivityCullTop(cls: Class<out Activity?>) {
        return ActivityStack.getInstance().finishActivityCullTop(cls)
    }


    fun finishAllActivityExcept(exceptActivityCls: Class<out Activity?>) {
        return ActivityStack.getInstance().finishAllActivityExcept(exceptActivityCls)
    }

    fun getString(id: Int): String {
        return context.getString(id)
    }

    fun getColor(resId: Int): Int {
        return ContextCompat.getColor(context, resId)
    }

    // 重启应用程序
    fun resetApp() {
        val intent = context.packageManager.getLaunchIntentForPackage(context.packageName)
        intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        context.startActivity(intent)
        AppExit()
    }

    fun AppExit() {
        try {
            finishAllActivity()
            // 杀死该应用进程
            Process.killProcess(Process.myPid())
            System.exit(0)
        } catch (e: Exception) {
        }
    }
}

enum class LifecycleEvent {
    CREATE, START, RESUME, PAUSE, STOP, DESTROY
}