# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
android.enableJetifier=true
org.gradle.daemon=false
#for vivo
android.injected.testOnly=false
# Final release package warning: api_env must be 'prod'
# Manual change api_env value to test|prod|dev
# Or cmd 'gradle assembleRelease -Papi_env=prod'
# org.gradle.jvmargs=-Xmx4096m -XX\:MaxPermSize\=1024m -XX\:+HeapDumpOnOutOfMemoryError -Dfile.encoding\=UTF-8
#org.gradle.configureondemand=false
api_env=test
#api_env=prod
app_channel=facsin
# uploadCrashlyticsMappingFileRelease solution:
# Generate release apk, the Firebase Crashlytics SDK needs to upload the project obfuscated Mapping
# and other files to Google's server,otherwise the crash log will be displayed in an obfuscated form.
# proxyHost: The address of the proxy host, if it is a Proxy software opened by your own
# computer, then you can fill in 127.0.0.1.
# proxyPort: The port address of the proxy. Proxy software is opened, go to the
# software settings to check its proxy port.
systemProp.https.proxyHost=127.0.0.1
systemProp.https.proxyPort=1087
android.defaults.buildfeatures.buildconfig=true
android.nonFinalResIds=false