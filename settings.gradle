pluginManagement {
    ext.kotlin_version = '1.7.10'

    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
        maven { url 'https://maven.aliyun.com/repository/public/' } // 阿里云镜像
        maven { url "https://maven.rongcloud.cn/repository/maven-releases/" }
    }
}

rootProject.name = "SeeingU_Chatter"
include ':app'
include ':imkit'
include ':lib_basecore'
include ':lib_common'
include ':lib_thirdparty'

include ':lib_gift'
