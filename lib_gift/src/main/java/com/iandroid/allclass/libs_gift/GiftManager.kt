package com.iandroid.allclass.libs_gift

import android.util.Log
import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.libs_gift.database.GiftDatabase
import com.iandroid.allclass.libs_gift.entity.GiftEntity
import com.iandroid.allclass.libs_gift.entity.toGiftEntity
import com.iandroid.allclass.libs_gift.model.GiftResourceInfo
import com.iandroid.allclass.libs_gift.model.GiftResourceType
import com.iandroid.allclass.libs_gift.model.PreloadProgress
import com.iandroid.allclass.libs_gift.model.PreloadResult
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import com.iandroid.allclass.libs_gift.listener.GiftPreloadListener
import java.util.concurrent.CopyOnWriteArrayList
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Semaphore

/**
 * Gift预加载管理器 - 单例模式
 * 支持批量预加载礼物资源，提供进度跟踪和并发控制
 * 不使用依赖注入，手动管理依赖关系
 */
class GiftManager private constructor() {
    companion object {
        private const val TAG = "GiftManager"
        private const val DEFAULT_CONCURRENCY = 3 // 默认并发数

        @Volatile
        private var INSTANCE: GiftManager? = null

        fun getInstance(): GiftManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GiftManager().also { INSTANCE = it }
            }
        }
    }

    // 手动管理的依赖
    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO
    private val giftDownloadManager: GiftDownloadManager = GiftDownloadManager
    private val database: GiftDatabase by lazy {
        GiftDatabase.getInstance(AppContext.context)
    }

    // 预加载作用域
    private val preloadScope = CoroutineScope(ioDispatcher + SupervisorJob())

    // 当前预加载任务
    private var currentPreloadJob: Job? = null

    // 预加载监听器列表（线程安全）
    private val preloadListeners = CopyOnWriteArrayList<GiftPreloadListener>()

    // 当前预加载状态
    private var currentProgress: PreloadProgress = PreloadProgress.Idle

    /**
     * 添加预加载监听器
     * @param listener 监听器实例
     */
    fun addPreloadListener(listener: GiftPreloadListener) {
        if (!preloadListeners.contains(listener)) {
            preloadListeners.add(listener)
            Log.d(TAG, "Added preload listener: ${listener.javaClass.simpleName}")
        }
    }

    /**
     * 移除预加载监听器
     * @param listener 监听器实例
     */
    fun removePreloadListener(listener: GiftPreloadListener) {
        if (preloadListeners.remove(listener)) {
            Log.d(TAG, "Removed preload listener: ${listener.javaClass.simpleName}")
        }
    }

    /**
     * 清除所有预加载监听器
     */
    fun clearPreloadListeners() {
        val count = preloadListeners.size
        preloadListeners.clear()
        Log.d(TAG, "Cleared $count preload listeners")
    }

    /**
     * 获取当前预加载状态
     */
    fun getCurrentProgress(): PreloadProgress = currentProgress

    /**
     * 通知所有监听器进度更新
     */
    private fun notifyProgressUpdate(progress: PreloadProgress) {
        currentProgress = progress
        preloadListeners.forEach { listener ->
            try {
                listener.onProgressUpdate(progress)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying progress to listener: ${listener.javaClass.simpleName}", e)
            }
        }
    }

    /**
     * 通知所有监听器预加载完成
     */
    private fun notifyPreloadComplete(result: PreloadResult) {
        val completedProgress = PreloadProgress.Completed(result)
        currentProgress = completedProgress
        preloadListeners.forEach { listener ->
            try {
                listener.onPreloadComplete(result)
                listener.onProgressUpdate(completedProgress)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying completion to listener: ${listener.javaClass.simpleName}", e)
            }
        }
    }

    /**
     * 通知所有监听器预加载开始
     */
    private fun notifyPreloadStart(totalCount: Int) {
        preloadListeners.forEach { listener ->
            try {
                listener.onPreloadStart(totalCount)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying start to listener: ${listener.javaClass.simpleName}", e)
            }
        }
    }

    /**
     * 通知所有监听器预加载取消
     */
    private fun notifyPreloadCancelled() {
        currentProgress = PreloadProgress.Idle
        preloadListeners.forEach { listener ->
            try {
                listener.onPreloadCancelled()
                listener.onProgressUpdate(PreloadProgress.Idle)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying cancellation to listener: ${listener.javaClass.simpleName}", e)
            }
        }
    }

    /**
     * 预加载礼物列表
     * @param gifts 要预加载的礼物列表
     * @param concurrency 并发数，默认为3
     * @param cleanupObsolete 是否清理过时的缓存数据，默认为true
     * @param onProgress 进度回调
     * @param onComplete 完成回调
     */
    fun preloadGifts(
        gifts: List<GiftResourceInfo>,
        concurrency: Int = DEFAULT_CONCURRENCY,
        cleanupObsolete: Boolean = true,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        // 取消之前的预加载任务
        cancelPreloading()

        if (gifts.isEmpty()) {
            val result = PreloadResult.Success(emptyList(), emptyList(), emptyList())
            notifyPreloadComplete(result)
            onComplete?.invoke(result)
            return
        }

        currentPreloadJob = preloadScope.launch {
            try {
                Log.d(TAG, "Starting preload for ${gifts.size} gifts with concurrency $concurrency")

                // 通知预加载开始
                notifyPreloadStart(gifts.size)

                // 更新进度为开始状态
                val startProgress = PreloadProgress.InProgress(0, gifts.size, emptyList(), emptyList())
                notifyProgressUpdate(startProgress)
                onProgress?.invoke(startProgress)

                val result = executePreload(gifts, concurrency, cleanupObsolete) { progress ->
                    notifyProgressUpdate(progress)
                    onProgress?.invoke(progress)
                }

                // 通知预加载完成
                notifyPreloadComplete(result)
                onComplete?.invoke(result)

                Log.d(TAG, "Preload completed: ${result.successfulGifts.size} success, ${result.failedGifts.size} failed")

            } catch (e: Exception) {
                Log.e(TAG, "Preload failed", e)
                val errorResult = PreloadResult.Error(e, emptyList(), gifts.map { it.giftId }, emptyList())
                notifyPreloadComplete(errorResult)
                onComplete?.invoke(errorResult)
            }
        }
    }

    /**
     * 预加载礼物列表（挂起函数版本）
     */
    suspend fun preloadGiftsAsync(
        gifts: List<GiftResourceInfo>,
        concurrency: Int = DEFAULT_CONCURRENCY,
        cleanupObsolete: Boolean = true,
        onProgress: (suspend (PreloadProgress) -> Unit)? = null
    ): PreloadResult {
        if (gifts.isEmpty()) {
            return PreloadResult.Success(emptyList(), emptyList(), emptyList())
        }

        return try {
            Log.d(TAG, "Starting async preload for ${gifts.size} gifts")

            val startProgress = PreloadProgress.InProgress(0, gifts.size, emptyList(), emptyList())
            onProgress?.invoke(startProgress)

            executePreload(gifts, concurrency, cleanupObsolete, onProgress)
        } catch (e: Exception) {
            Log.e(TAG, "Async preload failed", e)
            PreloadResult.Error(e, emptyList(), gifts.map { it.giftId }, emptyList())
        }
    }

    suspend fun getGiftEntity(giftId: String): GiftEntity? {
        return database.giftCacheDao().getGift(giftId)
    }

    /**
     * 取消当前预加载任务
     */
    fun cancelPreloading() {
        currentPreloadJob?.cancel()
        currentPreloadJob = null
        notifyPreloadCancelled()
        Log.d(TAG, "Preloading cancelled")
    }

    /**
     * 检查是否正在预加载
     */
    fun isPreloading(): Boolean {
        return currentPreloadJob?.isActive == true
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        cancelPreloading()
        preloadScope.cancel()
        clearPreloadListeners()
        Log.d(TAG, "GiftManager cleaned up")
    }

    /**
     * 将 GiftCacheEntity 列表转换为 GiftEntity 列表
     * @param cachedEntities 缓存的实体列表
     * @param originalGifts 原始的礼物信息列表
     */
    private fun convertToGiftEntities(
        cachedEntities: List<GiftEntity>,
        originalGifts: List<GiftResourceInfo>
    ): List<GiftEntity> {
        val giftInfoMap = originalGifts.associateBy { it.giftId }

        return cachedEntities.map { cacheEntity ->
            val originalInfo = giftInfoMap[cacheEntity.giftId]
            // 如果找不到原始信息，使用缓存信息创建基本的 GiftEntity
            originalInfo?.toGiftEntity(cacheEntity) ?: cacheEntity
        }
    }

    /**
     * 清理不在当前礼物列表中的过时缓存数据
     * @param currentGifts 当前要预加载的礼物列表
     */
    private suspend fun cleanupObsoleteGifts(currentGifts: List<GiftResourceInfo>) {
        try {
            // 获取当前传入的礼物ID集合
            val currentGiftIds = currentGifts.map { it.giftId }.toSet()

            // 获取数据库中所有的缓存礼物
            val allCachedGifts = database.giftCacheDao().getAllGifts()

            // 找出数据库中存在但当前列表中不存在的礼物ID
            val obsoleteGiftIds = allCachedGifts
                .map { it.giftId }
                .filter { it !in currentGiftIds }

            if (obsoleteGiftIds.isNotEmpty()) {
                Log.d(TAG, "Found ${obsoleteGiftIds.size} obsolete gifts to cleanup: $obsoleteGiftIds")

                // 删除过时的缓存数据
                database.giftCacheDao().deleteGifts(obsoleteGiftIds)

                // 删除对应的缓存文件
                deleteObsoleteFiles(obsoleteGiftIds)

                Log.d(TAG, "Successfully cleaned up ${obsoleteGiftIds.size} obsolete gifts")
            } else {
                Log.d(TAG, "No obsolete gifts found, database is up to date")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cleanup obsolete gifts", e)
            // 清理失败不应该影响预加载流程，所以只记录错误
        }
    }

    /**
     * 删除过时礼物的缓存文件
     * @param obsoleteGiftIds 过时的礼物ID列表
     */
    private suspend fun deleteObsoleteFiles(obsoleteGiftIds: List<String>) {
        obsoleteGiftIds.forEach { giftId ->
            try {
                // 删除图片文件
                val imageFile = giftDownloadManager.getGiftImageFile(giftId)
                if (imageFile.exists()) {
                    imageFile.delete()
                    Log.d(TAG, "Deleted obsolete image file: ${imageFile.absolutePath}")
                }

                // 删除GIF文件
                val gifFile = giftDownloadManager.getGiftGifFile(giftId)
                if (gifFile.exists()) {
                    gifFile.delete()
                    Log.d(TAG, "Deleted obsolete gif file: ${gifFile.absolutePath}")
                }

                // 删除视频文件
                val mp4File = giftDownloadManager.getGiftMp4File(giftId)
                if (mp4File.exists()) {
                    mp4File.delete()
                    Log.d(TAG, "Deleted obsolete video file: ${mp4File.absolutePath}")
                }
            } catch (e: Exception) {
                Log.w(TAG, "Failed to delete files for obsolete gift $giftId", e)
            }
        }
    }

    /**
     * 执行预加载逻辑
     */
    private suspend fun executePreload(
        gifts: List<GiftResourceInfo>,
        concurrency: Int,
        cleanupObsolete: Boolean = true,
        onProgress: (suspend (PreloadProgress.InProgress) -> Unit)? = null
    ): PreloadResult {
        // 清理不在当前礼物列表中的缓存数据
        if (cleanupObsolete) {
            cleanupObsoleteGifts(gifts)
        }

        val semaphore = Semaphore(concurrency)
        val successfulGifts = mutableListOf<String>()
        val failedGifts = mutableListOf<String>()

        // 使用async并发处理
        val deferredResults = gifts.map { gift ->
            preloadScope.async {
                semaphore.acquire()
                try {
                    // 尝试缓存礼物
                    giftDownloadManager.cacheGiftIfNeeded(gift)

                    synchronized(successfulGifts) {
                        successfulGifts.add(gift.giftId)
                    }

                    // 更新进度
                    val currentProgress = synchronized(successfulGifts) {
                        successfulGifts.size + failedGifts.size
                    }

                    // 获取当前成功的礼物缓存数据并转换为 GiftEntity
                    val currentCachedData = if (successfulGifts.isNotEmpty()) {
                        try {
                            val cachedEntities = database.giftCacheDao().getGifts(successfulGifts.toList())
                            convertToGiftEntities(cachedEntities, gifts)
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to get cached data for progress update", e)
                            emptyList()
                        }
                    } else {
                        emptyList()
                    }

                    onProgress?.invoke(
                        PreloadProgress.InProgress(
                            currentProgress,
                            gifts.size,
                            successfulGifts.toList(),
                            failedGifts.toList(),
                            currentCachedData
                        )
                    )

                    Log.d(TAG, "Successfully preloaded gift: ${gift.giftId}")
                    true
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to preload gift: ${gift.giftId}", e)

                    synchronized(failedGifts) {
                        failedGifts.add(gift.giftId)
                    }

                    // 更新进度
                    val currentProgress = synchronized(successfulGifts) {
                        successfulGifts.size + failedGifts.size
                    }

                    // 获取当前成功的礼物缓存数据并转换为 GiftEntity
                    val currentCachedData = if (successfulGifts.isNotEmpty()) {
                        try {
                            val cachedEntities = database.giftCacheDao().getGifts(successfulGifts.toList())
                            convertToGiftEntities(cachedEntities, gifts)
                        } catch (e: Exception) {
                            Log.w(TAG, "Failed to get cached data for progress update", e)
                            emptyList()
                        }
                    } else {
                        emptyList()
                    }

                    onProgress?.invoke(
                        PreloadProgress.InProgress(
                            currentProgress,
                            gifts.size,
                            successfulGifts.toList(),
                            failedGifts.toList(),
                            currentCachedData
                        )
                    )

                    false
                } finally {
                    semaphore.release()
                }
            }
        }

        // 等待所有任务完成
        deferredResults.awaitAll()

        // 从数据库获取成功缓存的礼物数据并转换为 GiftEntity
        val cachedGiftsData = if (successfulGifts.isNotEmpty()) {
            val cachedEntities = database.giftCacheDao().getGifts(successfulGifts)
            convertToGiftEntities(cachedEntities, gifts)
        } else {
            emptyList()
        }

        return if (failedGifts.isEmpty()) {
            PreloadResult.Success(successfulGifts, failedGifts, cachedGiftsData)
        } else {
            PreloadResult.PartialSuccess(successfulGifts, failedGifts, cachedGiftsData)
        }
    }

    /**
     * 预加载高优先级礼物
     * 优先预加载图片，然后是GIF，最后是MP4
     */
    fun preloadHighPriorityGifts(
        gifts: List<GiftResourceInfo>,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        // 按优先级排序：有图片的优先，然后是有GIF的，最后是有MP4的
        val sortedGifts = gifts.sortedWith { a, b ->
            val aScore = (if (a.image != null) 3 else 0) +
                        (if (a.gif != null) 2 else 0) +
                        (if (a.video != null) 1 else 0)
            val bScore = (if (b.image != null) 3 else 0) +
                        (if (b.gif != null) 2 else 0) +
                        (if (b.video != null) 1 else 0)
            bScore.compareTo(aScore) // 降序排列
        }

        preloadGifts(sortedGifts, DEFAULT_CONCURRENCY,  onProgress = onProgress, onComplete = onComplete)
    }

    /**
     * 预加载指定类型的资源
     * @param gifts 礼物列表
     * @param resourceTypes 要预加载的资源类型
     */
    fun preloadSpecificResources(
        gifts: List<GiftResourceInfo>,
        resourceTypes: Set<GiftResourceType>,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        // 过滤出包含指定资源类型的礼物
        val filteredGifts = gifts.filter { gift ->
            resourceTypes.any { type ->
                when (type) {
                    GiftResourceType.IMAGE -> gift.image != null
                    GiftResourceType.GIF -> gift.gif != null
                    GiftResourceType.MP4 -> gift.video != null
                }
            }
        }

        preloadGifts(filteredGifts, DEFAULT_CONCURRENCY, onProgress = onProgress, onComplete = onComplete)
    }

    /**
     * 批量预加载（分批处理大量礼物）
     * @param gifts 礼物列表
     * @param batchSize 每批处理的数量
     * @param batchDelay 批次间延迟（毫秒）
     * @param cleanupObsolete 是否清理过时的缓存数据，默认为true
     */
    fun preloadInBatches(
        gifts: List<GiftResourceInfo>,
        batchSize: Int = 10,
        batchDelay: Long = 1000,
        cleanupObsolete: Boolean = true,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        cancelPreloading()

        if (gifts.isEmpty()) {
            val result = PreloadResult.Success(emptyList(), emptyList(), emptyList())
            notifyPreloadComplete(result)
            onComplete?.invoke(result)
            return
        }

        currentPreloadJob = preloadScope.launch {
            try {
                // 通知预加载开始
                notifyPreloadStart(gifts.size)

                val allSuccessful = mutableListOf<String>()
                val allFailed = mutableListOf<String>()

                val batches = gifts.chunked(batchSize)

                batches.forEachIndexed { batchIndex, batch ->
                    Log.d(TAG, "Processing batch ${batchIndex + 1}/${batches.size} with ${batch.size} gifts")

                    val batchResult = executePreload(batch, DEFAULT_CONCURRENCY, cleanupObsolete && batchIndex == 0) { progress ->
                        // 计算总体进度
                        val totalProcessed = allSuccessful.size + allFailed.size + progress.completed
                        val combinedCachedData = try {
                            val allCurrentSuccessful = allSuccessful + progress.successfulGifts
                            if (allCurrentSuccessful.isNotEmpty()) {
                                val cachedEntities = database.giftCacheDao().getGifts(allCurrentSuccessful)
                                convertToGiftEntities(cachedEntities, gifts)
                            } else {
                                emptyList()
                            }
                        } catch (e: Exception) {
                            progress.cachedGifts
                        }

                        val overallProgress = PreloadProgress.InProgress(
                            totalProcessed,
                            gifts.size,
                            allSuccessful + progress.successfulGifts,
                            allFailed + progress.failedGifts,
                            combinedCachedData
                        )
                        notifyProgressUpdate(overallProgress)
                        onProgress?.invoke(overallProgress)
                    }

                    allSuccessful.addAll(batchResult.successfulGifts)
                    allFailed.addAll(batchResult.failedGifts)

                    // 批次间延迟
                    if (batchIndex < batches.size - 1) {
                        delay(batchDelay)
                    }
                }

                // 获取最终的缓存数据并转换为 GiftEntity
                val finalCachedData = if (allSuccessful.isNotEmpty()) {
                    val cachedEntities = database.giftCacheDao().getGifts(allSuccessful)
                    convertToGiftEntities(cachedEntities, gifts)
                } else {
                    emptyList()
                }

                val finalResult = if (allFailed.isEmpty()) {
                    PreloadResult.Success(allSuccessful, allFailed, finalCachedData)
                } else {
                    PreloadResult.PartialSuccess(allSuccessful, allFailed, finalCachedData)
                }

                notifyPreloadComplete(finalResult)
                onComplete?.invoke(finalResult)
                Log.d(TAG, "Batch preload completed: ${allSuccessful.size} success, ${allFailed.size} failed")

            } catch (e: Exception) {
                Log.e(TAG, "Batch preload failed", e)
                val errorResult = PreloadResult.Error(e, emptyList(), gifts.map { it.giftId }, emptyList())
                notifyPreloadComplete(errorResult)
                onComplete?.invoke(errorResult)
            }
        }
    }
}
