package com.flutterup.gifts

import com.flutterup.base.utils.Timber
import com.flutterup.gifts.database.GiftDatabase
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.gifts.entity.GiftResourceType
import com.flutterup.gifts.entity.PreloadProgress
import com.flutterup.gifts.entity.PreloadResult
import com.flutterup.gifts.entity.toGiftEntity
import com.flutterup.network.AppDispatchers
import com.flutterup.network.Dispatcher
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Semaphore
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Gift预加载管理器
 * 支持批量预加载礼物资源，提供进度跟踪和并发控制
 */
@Singleton
class GiftPreloadManager @Inject constructor(
    @Dispatcher(AppDispatchers.IO) private val ioDispatcher: CoroutineDispatcher,
    private val giftCacheManager: GiftCacheManager,
    private val database: GiftDatabase
) {
    companion object {
        private const val TAG = "GiftPreloadManager"
        private const val DEFAULT_CONCURRENCY = 3 // 默认并发数
    }

    // 预加载作用域
    private val preloadScope = CoroutineScope(ioDispatcher + SupervisorJob())

    // 当前预加载任务
    private var currentPreloadJob: Job? = null

    // 预加载进度状态
    private val _preloadProgress = MutableStateFlow<PreloadProgress>(PreloadProgress.Idle)
    val preloadProgress: StateFlow<PreloadProgress> = _preloadProgress.asStateFlow()

    /**
     * 预加载礼物列表
     * @param gifts 要预加载的礼物列表
     * @param concurrency 并发数，默认为3
     * @param cleanupObsolete 是否清理过时的缓存数据，默认为true
     * @param onProgress 进度回调
     * @param onComplete 完成回调
     */
    fun preloadGifts(
        gifts: List<GiftResourceInfo>,
        concurrency: Int = DEFAULT_CONCURRENCY,
        cleanupObsolete: Boolean = true,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        // 取消之前的预加载任务
        cancelPreloading()

        if (gifts.isEmpty()) {
            val result = PreloadResult.Success(emptyList(), emptyList(), emptyList())
            _preloadProgress.value = PreloadProgress.Completed(result)
            onComplete?.invoke(result)
            return
        }

        currentPreloadJob = preloadScope.launch {
            try {
                Timber.d(TAG, "Starting preload for ${gifts.size} gifts with concurrency $concurrency")

                // 更新进度为开始状态
                val startProgress = PreloadProgress.InProgress(0, gifts.size, emptyList(), emptyList())
                _preloadProgress.value = startProgress
                onProgress?.invoke(startProgress)

                val result = executePreload(gifts, concurrency, cleanupObsolete) { progress ->
                    _preloadProgress.value = progress
                    onProgress?.invoke(progress)
                }

                // 更新最终状态
                val finalProgress = PreloadProgress.Completed(result)
                _preloadProgress.value = finalProgress
                onComplete?.invoke(result)

                Timber.d(TAG, "Preload completed: ${result.successfulGifts.size} success, ${result.failedGifts.size} failed")

            } catch (e: Exception) {
                Timber.e(TAG, "Preload failed", e)
                val errorResult = PreloadResult.Error(e, emptyList(), gifts.map { it.giftId }, emptyList())
                val errorProgress = PreloadProgress.Completed(errorResult)
                _preloadProgress.value = errorProgress
                onComplete?.invoke(errorResult)
            }
        }
    }

    /**
     * 预加载礼物列表（挂起函数版本）
     */
    suspend fun preloadGiftsAsync(
        gifts: List<GiftResourceInfo>,
        concurrency: Int = DEFAULT_CONCURRENCY,
        cleanupObsolete: Boolean = true,
        onProgress: (suspend (PreloadProgress) -> Unit)? = null
    ): PreloadResult {
        if (gifts.isEmpty()) {
            return PreloadResult.Success(emptyList(), emptyList(), emptyList())
        }

        return try {
            Timber.d(TAG, "Starting async preload for ${gifts.size} gifts")

            val startProgress = PreloadProgress.InProgress(0, gifts.size, emptyList(), emptyList())
            onProgress?.invoke(startProgress)

            executePreload(gifts, concurrency, cleanupObsolete, onProgress)
        } catch (e: Exception) {
            Timber.e(TAG, "Async preload failed", e)
            PreloadResult.Error(e, emptyList(), gifts.map { it.giftId }, emptyList())
        }
    }

    suspend fun getGiftEntity(giftId: String): GiftEntity? {
        return database.giftCacheDao().getGift(giftId)
    }

    /**
     * 取消当前预加载任务
     */
    fun cancelPreloading() {
        currentPreloadJob?.cancel()
        currentPreloadJob = null
        _preloadProgress.value = PreloadProgress.Idle
        Timber.d(TAG, "Preloading cancelled")
    }

    /**
     * 检查是否正在预加载
     */
    fun isPreloading(): Boolean {
        return currentPreloadJob?.isActive == true
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        cancelPreloading()
        preloadScope.cancel()
        Timber.d(TAG, "GiftPreloadManager cleaned up")
    }

    /**
     * 将 GiftCacheEntity 列表转换为 GiftEntity 列表
     * @param cachedEntities 缓存的实体列表
     * @param originalGifts 原始的礼物信息列表
     */
    private fun convertToGiftEntities(
        cachedEntities: List<GiftEntity>,
        originalGifts: List<GiftResourceInfo>
    ): List<GiftEntity> {
        val giftInfoMap = originalGifts.associateBy { it.giftId }

        return cachedEntities.map { cacheEntity ->
            val originalInfo = giftInfoMap[cacheEntity.giftId]
            // 如果找不到原始信息，使用缓存信息创建基本的 GiftEntity
            originalInfo?.toGiftEntity(cacheEntity) ?: cacheEntity
        }
    }

    /**
     * 清理不在当前礼物列表中的过时缓存数据
     * @param currentGifts 当前要预加载的礼物列表
     */
    private suspend fun cleanupObsoleteGifts(currentGifts: List<GiftResourceInfo>) {
        try {
            // 获取当前传入的礼物ID集合
            val currentGiftIds = currentGifts.map { it.giftId }.toSet()

            // 获取数据库中所有的缓存礼物
            val allCachedGifts = database.giftCacheDao().getAllGifts()

            // 找出数据库中存在但当前列表中不存在的礼物ID
            val obsoleteGiftIds = allCachedGifts
                .map { it.giftId }
                .filter { it !in currentGiftIds }

            if (obsoleteGiftIds.isNotEmpty()) {
                Timber.d(TAG, "Found ${obsoleteGiftIds.size} obsolete gifts to cleanup: $obsoleteGiftIds")

                // 删除过时的缓存数据
                database.giftCacheDao().deleteGifts(obsoleteGiftIds)

                // 删除对应的缓存文件
                deleteObsoleteFiles(obsoleteGiftIds)

                Timber.d(TAG, "Successfully cleaned up ${obsoleteGiftIds.size} obsolete gifts")
            } else {
                Timber.d(TAG, "No obsolete gifts found, database is up to date")
            }
        } catch (e: Exception) {
            Timber.e(TAG, "Failed to cleanup obsolete gifts", e)
            // 清理失败不应该影响预加载流程，所以只记录错误
        }
    }

    /**
     * 删除过时礼物的缓存文件
     * @param obsoleteGiftIds 过时的礼物ID列表
     */
    private suspend fun deleteObsoleteFiles(obsoleteGiftIds: List<String>) {
        obsoleteGiftIds.forEach { giftId ->
            try {
                // 删除图片文件
                val imageFile = giftCacheManager.getGiftImageFile(giftId)
                if (imageFile.exists()) {
                    imageFile.delete()
                    Timber.d(TAG, "Deleted obsolete image file: ${imageFile.absolutePath}")
                }

                // 删除GIF文件
                val gifFile = giftCacheManager.getGiftGifFile(giftId)
                if (gifFile.exists()) {
                    gifFile.delete()
                    Timber.d(TAG, "Deleted obsolete gif file: ${gifFile.absolutePath}")
                }

                // 删除视频文件
                val mp4File = giftCacheManager.getGiftMp4File(giftId)
                if (mp4File.exists()) {
                    mp4File.delete()
                    Timber.d(TAG, "Deleted obsolete video file: ${mp4File.absolutePath}")
                }
            } catch (e: Exception) {
                Timber.w(TAG, "Failed to delete files for obsolete gift $giftId", e)
            }
        }
    }

    /**
     * 执行预加载逻辑
     */
    private suspend fun executePreload(
        gifts: List<GiftResourceInfo>,
        concurrency: Int,
        cleanupObsolete: Boolean = true,
        onProgress: (suspend (PreloadProgress.InProgress) -> Unit)? = null
    ): PreloadResult {
        // 清理不在当前礼物列表中的缓存数据
        if (cleanupObsolete) {
            cleanupObsoleteGifts(gifts)
        }

        val semaphore = Semaphore(concurrency)
        val successfulGifts = mutableListOf<String>()
        val failedGifts = mutableListOf<String>()

        // 使用async并发处理
        val deferredResults = gifts.map { gift ->
            preloadScope.async {
                semaphore.acquire()
                try {
                    // 尝试缓存礼物
                    giftCacheManager.cacheGiftIfNeeded(gift)

                    synchronized(successfulGifts) {
                        successfulGifts.add(gift.giftId)
                    }

                    // 更新进度
                    val currentProgress = synchronized(successfulGifts) {
                        successfulGifts.size + failedGifts.size
                    }

                    // 获取当前成功的礼物缓存数据并转换为 GiftEntity
                    val currentCachedData = if (successfulGifts.isNotEmpty()) {
                        try {
                            val cachedEntities = database.giftCacheDao().getGifts(successfulGifts.toList())
                            convertToGiftEntities(cachedEntities, gifts)
                        } catch (e: Exception) {
                            Timber.w(TAG, "Failed to get cached data for progress update", e)
                            emptyList()
                        }
                    } else {
                        emptyList()
                    }

                    onProgress?.invoke(
                        PreloadProgress.InProgress(
                            currentProgress,
                            gifts.size,
                            successfulGifts.toList(),
                            failedGifts.toList(),
                            currentCachedData
                        )
                    )

                    Timber.d(TAG, "Successfully preloaded gift: ${gift.giftId}")
                    true
                } catch (e: Exception) {
                    Timber.e(TAG, "Failed to preload gift: ${gift.giftId}", e)

                    synchronized(failedGifts) {
                        failedGifts.add(gift.giftId)
                    }

                    // 更新进度
                    val currentProgress = synchronized(successfulGifts) {
                        successfulGifts.size + failedGifts.size
                    }

                    // 获取当前成功的礼物缓存数据并转换为 GiftEntity
                    val currentCachedData = if (successfulGifts.isNotEmpty()) {
                        try {
                            val cachedEntities = database.giftCacheDao().getGifts(successfulGifts.toList())
                            convertToGiftEntities(cachedEntities, gifts)
                        } catch (e: Exception) {
                            Timber.w(TAG, "Failed to get cached data for progress update", e)
                            emptyList()
                        }
                    } else {
                        emptyList()
                    }

                    onProgress?.invoke(
                        PreloadProgress.InProgress(
                            currentProgress,
                            gifts.size,
                            successfulGifts.toList(),
                            failedGifts.toList(),
                            currentCachedData
                        )
                    )

                    false
                } finally {
                    semaphore.release()
                }
            }
        }

        // 等待所有任务完成
        deferredResults.awaitAll()

        // 从数据库获取成功缓存的礼物数据并转换为 GiftEntity
        val cachedGiftsData = if (successfulGifts.isNotEmpty()) {
            val cachedEntities = database.giftCacheDao().getGifts(successfulGifts)
            convertToGiftEntities(cachedEntities, gifts)
        } else {
            emptyList()
        }

        return if (failedGifts.isEmpty()) {
            PreloadResult.Success(successfulGifts, failedGifts, cachedGiftsData)
        } else {
            PreloadResult.PartialSuccess(successfulGifts, failedGifts, cachedGiftsData)
        }
    }

    /**
     * 预加载高优先级礼物
     * 优先预加载图片，然后是GIF，最后是MP4
     */
    fun preloadHighPriorityGifts(
        gifts: List<GiftResourceInfo>,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        // 按优先级排序：有图片的优先，然后是有GIF的，最后是有MP4的
        val sortedGifts = gifts.sortedWith { a, b ->
            val aScore = (if (a.image != null) 3 else 0) +
                        (if (a.gif != null) 2 else 0) +
                        (if (a.video != null) 1 else 0)
            val bScore = (if (b.image != null) 3 else 0) +
                        (if (b.gif != null) 2 else 0) +
                        (if (b.video != null) 1 else 0)
            bScore.compareTo(aScore) // 降序排列
        }

        preloadGifts(sortedGifts, DEFAULT_CONCURRENCY,  onProgress = onProgress, onComplete = onComplete)
    }

    /**
     * 预加载指定类型的资源
     * @param gifts 礼物列表
     * @param resourceTypes 要预加载的资源类型
     */
    fun preloadSpecificResources(
        gifts: List<GiftResourceInfo>,
        resourceTypes: Set<GiftResourceType>,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        // 过滤出包含指定资源类型的礼物
        val filteredGifts = gifts.filter { gift ->
            resourceTypes.any { type ->
                when (type) {
                    GiftResourceType.IMAGE -> gift.image != null
                    GiftResourceType.GIF -> gift.gif != null
                    GiftResourceType.MP4 -> gift.video != null
                }
            }
        }

        preloadGifts(filteredGifts, DEFAULT_CONCURRENCY, onProgress = onProgress, onComplete = onComplete)
    }

    /**
     * 批量预加载（分批处理大量礼物）
     * @param gifts 礼物列表
     * @param batchSize 每批处理的数量
     * @param batchDelay 批次间延迟（毫秒）
     * @param cleanupObsolete 是否清理过时的缓存数据，默认为true
     */
    fun preloadInBatches(
        gifts: List<GiftResourceInfo>,
        batchSize: Int = 10,
        batchDelay: Long = 1000,
        cleanupObsolete: Boolean = true,
        onProgress: ((PreloadProgress) -> Unit)? = null,
        onComplete: ((PreloadResult) -> Unit)? = null
    ) {
        cancelPreloading()

        if (gifts.isEmpty()) {
            val result = PreloadResult.Success(emptyList(), emptyList(), emptyList())
            onComplete?.invoke(result)
            return
        }

        currentPreloadJob = preloadScope.launch {
            try {
                val allSuccessful = mutableListOf<String>()
                val allFailed = mutableListOf<String>()

                val batches = gifts.chunked(batchSize)

                batches.forEachIndexed { batchIndex, batch ->
                    Timber.d(TAG, "Processing batch ${batchIndex + 1}/${batches.size} with ${batch.size} gifts")

                    val batchResult = executePreload(batch, DEFAULT_CONCURRENCY, cleanupObsolete && batchIndex == 0) { progress ->
                        // 计算总体进度
                        val totalProcessed = allSuccessful.size + allFailed.size + progress.completed
                        val combinedCachedData = try {
                            val allCurrentSuccessful = allSuccessful + progress.successfulGifts
                            if (allCurrentSuccessful.isNotEmpty()) {
                                val cachedEntities = database.giftCacheDao().getGifts(allCurrentSuccessful)
                                convertToGiftEntities(cachedEntities, gifts)
                            } else {
                                emptyList()
                            }
                        } catch (e: Exception) {
                            progress.cachedGifts
                        }

                        val overallProgress = PreloadProgress.InProgress(
                            totalProcessed,
                            gifts.size,
                            allSuccessful + progress.successfulGifts,
                            allFailed + progress.failedGifts,
                            combinedCachedData
                        )
                        onProgress?.invoke(overallProgress)
                    }

                    allSuccessful.addAll(batchResult.successfulGifts)
                    allFailed.addAll(batchResult.failedGifts)

                    // 批次间延迟
                    if (batchIndex < batches.size - 1) {
                        kotlinx.coroutines.delay(batchDelay)
                    }
                }

                // 获取最终的缓存数据并转换为 GiftEntity
                val finalCachedData = if (allSuccessful.isNotEmpty()) {
                    val cachedEntities = database.giftCacheDao().getGifts(allSuccessful)
                    convertToGiftEntities(cachedEntities, gifts)
                } else {
                    emptyList()
                }

                val finalResult = if (allFailed.isEmpty()) {
                    PreloadResult.Success(allSuccessful, allFailed, finalCachedData)
                } else {
                    PreloadResult.PartialSuccess(allSuccessful, allFailed, finalCachedData)
                }

                onComplete?.invoke(finalResult)
                Timber.d(TAG, "Batch preload completed: ${allSuccessful.size} success, ${allFailed.size} failed")

            } catch (e: Exception) {
                Timber.e(TAG, "Batch preload failed", e)
                val errorResult = PreloadResult.Error(e, emptyList(), gifts.map { it.giftId }, emptyList())
                onComplete?.invoke(errorResult)
            }
        }
    }
}
