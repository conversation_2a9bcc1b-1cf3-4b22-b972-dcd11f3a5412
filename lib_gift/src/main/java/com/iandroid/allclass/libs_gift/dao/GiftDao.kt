package com.iandroid.allclass.libs_gift.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.iandroid.allclass.libs_gift.entity.GiftEntity

@Dao
interface GiftDao {

    @Query("SELECT * FROM gift WHERE giftId = :giftId")
    suspend fun getGift(giftId: String): GiftEntity?

    @Query("SELECT * FROM gift WHERE giftId IN (:giftIds)")
    suspend fun getGifts(giftIds: List<String>): List<GiftEntity>

    @Query("SELECT * FROM gift")
    suspend fun getAllGifts(): List<GiftEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGift(gift: GiftEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGifts(gifts: List<GiftEntity>)

    @Query("DELETE FROM gift WHERE giftId = :giftId")
    suspend fun deleteGift(giftId: String)

    @Query("DELETE FROM gift WHERE giftId IN (:giftIds)")
    suspend fun deleteGifts(giftIds: List<String>)
}