package com.iandroid.allclass.libs_gift.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.iandroid.allclass.libs_gift.dao.GiftDao
import com.iandroid.allclass.libs_gift.entity.GiftEntity


@Database(entities = [GiftEntity::class], version = 1)
abstract class GiftDatabase : RoomDatabase() {

    abstract fun giftCacheDao(): GiftDao

    companion object {
        @Volatile private var INSTANCE: GiftDatabase? = null

        fun getInstance(context: Context): GiftDatabase {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: Room.databaseBuilder(
                    context.applicationContext,
                    GiftDatabase::class.java,
                    "gift_cache.db"
                ).build().also { INSTANCE = it }
            }
        }
    }
}
