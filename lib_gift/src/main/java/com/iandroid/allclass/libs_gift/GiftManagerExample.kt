package com.iandroid.allclass.libs_gift

import com.iandroid.allclass.libs_gift.model.GiftResourceInfo
import com.iandroid.allclass.libs_gift.model.GiftResourceType
import com.iandroid.allclass.libs_gift.model.PreloadProgress
import com.iandroid.allclass.libs_gift.model.PreloadResult
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * GiftManager 和 GiftDownloadManager 使用示例
 * 展示如何使用单例模式的管理器
 */
object GiftManagerExample {
    
    /**
     * 示例：使用GiftDownloadManager下载单个礼物
     */
    fun downloadSingleGiftExample() {
        // 创建礼物资源信息
        val giftInfo = GiftResourceInfo(
            giftId = "gift_001",
            name = "玫瑰花",
            price = 10,
            image = "https://example.com/images/rose.png",
            gif = "https://example.com/gifs/rose.gif",
            video = "https://example.com/videos/rose.mp4",
            desc = "美丽的玫瑰花",
            version = 1L
        )
        
        // 使用单例获取GiftDownloadManager实例
        val downloadManager = GiftDownloadManager
        
        // 在协程中执行下载操作
        CoroutineScope(Dispatchers.Main).launch {
            try {
                downloadManager.cacheGiftIfNeeded(giftInfo)
                println("礼物下载成功: ${giftInfo.name}")
            } catch (e: Exception) {
                println("礼物下载失败: ${e.message}")
            }
        }
    }
    
    /**
     * 示例：使用GiftManager批量预加载礼物
     */
    fun preloadGiftsExample() {
        val giftList = listOf(
            GiftResourceInfo(
                giftId = "gift_001",
                name = "玫瑰花",
                price = 10,
                image = "https://example.com/images/rose.png",
                gif = "https://example.com/gifs/rose.gif",
                video = "https://example.com/videos/rose.mp4",
                desc = "美丽的玫瑰花",
                version = 1L
            ),
            GiftResourceInfo(
                giftId = "gift_002",
                name = "巧克力",
                price = 20,
                image = "https://example.com/images/chocolate.png",
                gif = "https://example.com/gifs/chocolate.gif",
                video = "https://example.com/videos/chocolate.mp4",
                desc = "甜蜜的巧克力",
                version = 1L
            )
        )
        
        // 使用单例获取GiftManager实例
        val giftManager = GiftManager.getInstance()
        
        // 预加载礼物，带进度回调
        giftManager.preloadGifts(
            gifts = giftList,
            concurrency = 3,
            onProgress = { progress ->
                when (progress) {
                    is PreloadProgress.Idle -> {
                        println("预加载状态：空闲")
                    }
                    is PreloadProgress.InProgress -> {
                        println("预加载进度：${progress.completed}/${progress.total} (${progress.progressPercentage}%)")
                        println("成功：${progress.successfulGifts.size}, 失败：${progress.failedGifts.size}")
                    }
                    is PreloadProgress.Completed -> {
                        println("预加载完成：${progress.result}")
                    }
                }
            },
            onComplete = { result ->
                when (result) {
                    is PreloadResult.Success -> {
                        println("预加载完全成功！成功数量：${result.successfulGifts.size}")
                    }
                    is PreloadResult.PartialSuccess -> {
                        println("预加载部分成功！成功：${result.successfulGifts.size}, 失败：${result.failedGifts.size}")
                    }
                    is PreloadResult.Error -> {
                        println("预加载失败：${result.error.message}")
                    }
                }
            }
        )
    }
    
    /**
     * 示例：预加载高优先级礼物
     */
    fun preloadHighPriorityGiftsExample() {
        val giftList = listOf(
            GiftResourceInfo(
                giftId = "gift_001",
                name = "玫瑰花",
                image = "https://example.com/images/rose.png",
                gif = "https://example.com/gifs/rose.gif",
                video = "https://example.com/videos/rose.mp4",
                version = 1L
            ),
            GiftResourceInfo(
                giftId = "gift_002", 
                name = "巧克力",
                image = "https://example.com/images/chocolate.png",
                version = 1L
            )
        )
        
        val giftManager = GiftManager.getInstance()
        
        // 按优先级预加载（图片 > GIF > MP4）
        giftManager.preloadHighPriorityGifts(
            gifts = giftList,
            onProgress = { progress ->
                if (progress is PreloadProgress.InProgress) {
                    println("高优先级预加载进度：${progress.progressPercentage}%")
                }
            },
            onComplete = { result ->
                println("高优先级预加载完成，成功率：${(result.successRate * 100).toInt()}%")
            }
        )
    }
    
    /**
     * 示例：预加载指定类型的资源
     */
    fun preloadSpecificResourcesExample() {
        val giftList = listOf(
            GiftResourceInfo(
                giftId = "gift_001",
                name = "玫瑰花",
                image = "https://example.com/images/rose.png",
                gif = "https://example.com/gifs/rose.gif",
                video = "https://example.com/videos/rose.mp4",
                version = 1L
            )
        )
        
        val giftManager = GiftManager.getInstance()
        
        // 只预加载图片和GIF，不预加载视频
        giftManager.preloadSpecificResources(
            gifts = giftList,
            resourceTypes = setOf(GiftResourceType.IMAGE, GiftResourceType.GIF),
            onComplete = { result ->
                println("指定类型预加载完成：${result.successfulGifts.size} 个礼物")
            }
        )
    }
    
    /**
     * 示例：分批预加载大量礼物
     */
    fun batchPreloadExample() {
        // 创建大量礼物数据
        val largeGiftList = (1..50).map { index ->
            GiftResourceInfo(
                giftId = "gift_${String.format("%03d", index)}",
                name = "礼物$index",
                image = "https://example.com/images/gift_$index.png",
                version = 1L
            )
        }
        
        val giftManager = GiftManager.getInstance()
        
        // 分批预加载，每批10个，批次间延迟1秒
        giftManager.preloadInBatches(
            gifts = largeGiftList,
            batchSize = 10,
            batchDelay = 1000,
            onProgress = { progress ->
                if (progress is PreloadProgress.InProgress) {
                    println("批量预加载进度：${progress.completed}/${progress.total}")
                }
            },
            onComplete = { result ->
                println("批量预加载完成！总计：${result.totalCount}, 成功：${result.successfulGifts.size}")
            }
        )
    }
    
    /**
     * 示例：获取礼物文件路径
     */
    fun getGiftFilePathsExample() {
        val downloadManager = GiftDownloadManager
        val giftId = "gift_001"
        
        // 获取各种类型的文件路径
        val imageFile = downloadManager.getGiftImageFile(giftId)
        val gifFile = downloadManager.getGiftGifFile(giftId)
        val mp4File = downloadManager.getGiftMp4File(giftId)
        
        println("图片文件路径: ${imageFile.absolutePath}")
        println("GIF文件路径: ${gifFile.absolutePath}")
        println("MP4文件路径: ${mp4File.absolutePath}")
        
        // 检查文件是否存在
        println("图片文件是否存在: ${imageFile.exists()}")
        println("GIF文件是否存在: ${gifFile.exists()}")
        println("MP4文件是否存在: ${mp4File.exists()}")
    }
    
    /**
     * 示例：管理预加载任务
     */
    fun managePreloadTasksExample() {
        val giftManager = GiftManager.getInstance()
        
        // 检查是否正在预加载
        if (giftManager.isPreloading()) {
            println("当前正在预加载中...")
            
            // 取消当前预加载
            giftManager.cancelPreloading()
            println("已取消预加载")
        }
        
        // 在应用退出时清理资源
        giftManager.cleanup()
        println("已清理GiftManager资源")
    }
}
