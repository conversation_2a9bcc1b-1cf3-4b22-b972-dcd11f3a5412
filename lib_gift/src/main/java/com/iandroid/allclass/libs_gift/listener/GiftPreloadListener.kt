package com.iandroid.allclass.libs_gift.listener

import com.iandroid.allclass.libs_gift.model.PreloadProgress
import com.iandroid.allclass.libs_gift.model.PreloadResult

/**
 * 礼物预加载监听器接口
 * 用于监听预加载进度和结果
 */
interface GiftPreloadListener {
    
    /**
     * 预加载进度更新回调
     * @param progress 当前进度状态
     */
    fun onProgressUpdate(progress: PreloadProgress)
    
    /**
     * 预加载完成回调
     * @param result 预加载结果
     */
    fun onPreloadComplete(result: PreloadResult)
    
    /**
     * 预加载开始回调
     * @param totalCount 总数量
     */
    fun onPreloadStart(totalCount: Int) {}
    
    /**
     * 预加载取消回调
     */
    fun onPreloadCancelled() {}
}

/**
 * 简化的预加载监听器抽象类
 * 可以选择性重写需要的方法
 */
abstract class SimpleGiftPreloadListener : GiftPreloadListener {
    
    override fun onProgressUpdate(progress: PreloadProgress) {
        // 默认空实现，子类可选择重写
    }
    
    override fun onPreloadComplete(result: PreloadResult) {
        // 默认空实现，子类可选择重写
    }
    
    override fun onPreloadStart(totalCount: Int) {
        // 默认空实现，子类可选择重写
    }
    
    override fun onPreloadCancelled() {
        // 默认空实现，子类可选择重写
    }
}
