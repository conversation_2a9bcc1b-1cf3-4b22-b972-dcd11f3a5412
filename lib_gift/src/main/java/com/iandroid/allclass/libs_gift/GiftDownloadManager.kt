package com.iandroid.allclass.libs_gift

import com.iandroid.allclass.lib_common.AppContext
import com.iandroid.allclass.libs_gift.database.GiftDatabase
import com.iandroid.allclass.libs_gift.entity.GiftEntity
import com.iandroid.allclass.libs_gift.model.GiftResourceInfo
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.io.FileOutputStream

object GiftDownloadManager {

    private val dispatchers: CoroutineDispatcher = Dispatchers.IO
    private val fileDir: File by lazy {
        AppContext.context.cacheDir
    }
    private val okHttpClient: OkHttpClient by lazy {
        OkHttpClient.Builder().build()
    }
    private val database: GiftDatabase by lazy {
        GiftDatabase.getInstance(AppContext.context)
    }

    suspend fun cacheGiftIfNeeded(info: GiftResourceInfo) {
        val cachedGift = database.giftCacheDao().getGift(info.giftId)

        val imageFile = getGiftImageFile(info.giftId)
        val gifFile = getGiftGifFile(info.giftId)
        val mp4File = getGiftMp4File(info.giftId)

        var needDownloadImage = true
        var needDownloadGif = true
        var needDownloadMp4 = true

        if (cachedGift != null) {
            if (cachedGift.version >= info.version) { // 版本相同，检查文件是否存在
                if (imageFile.exists()) {
                    needDownloadImage = false
                }
                if (gifFile.exists()) {
                    needDownloadGif = false
                }
                if (mp4File.exists()) {
                    needDownloadMp4 = false
                }
            }
        }

        if (needDownloadImage && info.image != null) {
            downloadToFile(info.image, imageFile)
        }
        if (needDownloadGif && info.gif != null) {
            downloadToFile(info.gif, gifFile)
        }
        if (needDownloadMp4 && info.video != null) {
            downloadToFile(info.video, mp4File)
        }

        if (needDownloadImage || needDownloadGif || needDownloadMp4) {
            insertGifResource(info, imageFile, gifFile, mp4File)
        }
    }

    private suspend fun downloadToFile(url: String, destFile: File): File? {
        return withContext(dispatchers) {
            try {
                destFile.parentFile?.mkdirs()

                val request = Request.Builder()
                    .url(url)
                    .build()

                okHttpClient.newCall(request).execute().use { response ->
                    if (response.isSuccessful) {
                        response.body?.byteStream()?.use { input ->
                            FileOutputStream(destFile).use { output ->
                                input.copyTo(output)
                            }
                        }
                        destFile
                    } else {
                        null
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    fun getGiftImageFile(giftId: String, suffix: String = IMAGE_SUFFIX): File {
        return File(fileDir, "${IMAGE_DIR}/$giftId$suffix")
    }

    fun getGiftGifFile(giftId: String, suffix: String = GIF_SUFFIX): File {
        return File(fileDir, "${GIF_DIR}/$giftId$suffix")
    }

    fun getGiftMp4File(giftId: String, suffix: String = MP4_SUFFIX): File {
        return File(fileDir, "${MP4_DIR}/$giftId$suffix")
    }

    private suspend fun insertGifResource(info: GiftResourceInfo, imageFile: File, gifFile: File, mp4File: File) {
        val entity = GiftEntity(
            giftId = info.giftId,
            name = info.name,
            price = info.price,
            imageUrl = info.image,
            imagePath = imageFile.absolutePath,
            gifUrl = info.gif,
            gifPath = gifFile.absolutePath,
            videoUrl = info.video,
            videoPath = mp4File.absolutePath,
            desc = info.desc,
            version = info.version
        )

        database.giftCacheDao().insertGift(entity)
    }
}


private const val IMAGE_SUFFIX = ".png"
private const val GIF_SUFFIX = ".gif"
private const val MP4_SUFFIX = ".mp4"
private const val IMAGE_DIR = "gift/image"
private const val GIF_DIR = "gift/gif"
private const val MP4_DIR = "gift/mp4"