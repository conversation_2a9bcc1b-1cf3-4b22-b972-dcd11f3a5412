# Gift管理模块

这个模块提供了礼物资源的下载和预加载功能，采用单例模式设计，无需依赖注入框架。

## 主要组件

### 1. GiftDownloadManager (单例对象)
负责单个礼物资源的下载和缓存管理。

**特性：**
- 支持图片、GIF、MP4视频下载
- 版本控制，避免重复下载
- 自动创建目录结构
- 使用OkHttp进行网络请求

**使用方式：**
```kotlin
// 直接使用对象
val downloadManager = GiftDownloadManager

// 下载礼物资源
downloadManager.cacheGiftIfNeeded(giftInfo)

// 获取文件路径
val imageFile = downloadManager.getGiftImageFile(giftId)
val gifFile = downloadManager.getGiftGifFile(giftId)
val mp4File = downloadManager.getGiftMp4File(giftId)
```

### 2. GiftManager (单例类)
提供批量预加载功能，支持进度跟踪和并发控制。

**特性：**
- 批量预加载礼物资源
- 实时进度跟踪
- 并发控制（默认3个并发）
- 支持高优先级预加载
- 支持指定资源类型预加载
- 支持分批处理大量数据
- 自动清理过时缓存

**使用方式：**
```kotlin
// 获取单例实例
val giftManager = GiftManager.getInstance()

// 基本预加载
giftManager.preloadGifts(
    gifts = giftList,
    onProgress = { progress -> 
        // 处理进度更新
    },
    onComplete = { result ->
        // 处理完成结果
    }
)

// 高优先级预加载
giftManager.preloadHighPriorityGifts(giftList)

// 指定类型预加载
giftManager.preloadSpecificResources(
    gifts = giftList,
    resourceTypes = setOf(GiftResourceType.IMAGE, GiftResourceType.GIF)
)

// 分批预加载
giftManager.preloadInBatches(
    gifts = largeGiftList,
    batchSize = 10,
    batchDelay = 1000
)
```

## 数据模型

### GiftResourceInfo
礼物资源信息数据类：
```kotlin
data class GiftResourceInfo(
    val giftId: String,
    val name: String?,
    val price: Int?,
    val image: String?,      // 图片URL
    val gif: String?,        // GIF URL
    val video: String?,      // 视频URL
    val desc: String?,
    val version: Long
)
```

### PreloadProgress
预加载进度状态：
```kotlin
sealed class PreloadProgress {
    object Idle : PreloadProgress()
    data class InProgress(...) : PreloadProgress()
    data class Completed(val result: PreloadResult) : PreloadProgress()
}
```

### PreloadResult
预加载结果：
```kotlin
sealed class PreloadResult {
    data class Success(...) : PreloadResult()
    data class PartialSuccess(...) : PreloadResult()
    data class Error(...) : PreloadResult()
}
```

## 目录结构

```
lib_gift/
├── src/main/java/com/iandroid/allclass/libs_gift/
│   ├── GiftDownloadManager.kt          # 下载管理器
│   ├── GiftManager.kt                  # 预加载管理器
│   ├── GiftManagerExample.kt           # 使用示例
│   ├── dao/
│   │   └── GiftDao.kt                  # 数据访问对象
│   ├── database/
│   │   └── GiftDatabase.kt             # 数据库
│   ├── entity/
│   │   └── GiftEntity.kt               # 数据库实体
│   └── model/
│       ├── GiftResourceInfo.kt         # 礼物资源信息
│       ├── GiftResourceType.kt         # 资源类型枚举
│       ├── PreloadProgress.kt          # 预加载进度
│       └── PreloadResult.kt            # 预加载结果
├── MIGRATION_GUIDE.md                  # 迁移指南
└── README.md                           # 本文档
```

## 缓存目录结构

```
{cacheDir}/
├── gift/
│   ├── image/
│   │   ├── gift_001.png
│   │   └── gift_002.png
│   ├── gif/
│   │   ├── gift_001.gif
│   │   └── gift_002.gif
│   └── mp4/
│       ├── gift_001.mp4
│       └── gift_002.mp4
```

## 依赖

在 `lib_gift/build.gradle` 中已添加：
```gradle
implementation 'com.squareup.okhttp3:okhttp:4.8.0'
implementation "androidx.room:room-runtime:2.4.0"
kapt "androidx.room:room-compiler:2.4.0"
```

## 注意事项

1. **线程安全**：所有单例实现都是线程安全的
2. **内存管理**：使用ApplicationContext避免内存泄漏
3. **错误处理**：所有网络请求都有异常处理
4. **资源清理**：在应用退出时调用 `giftManager.cleanup()`
5. **版本控制**：支持礼物资源版本更新

## 示例代码

详细的使用示例请参考 `GiftManagerExample.kt` 文件。

## 迁移指南

如果你正在从依赖注入版本迁移，请参考 `MIGRATION_GUIDE.md` 文档。
