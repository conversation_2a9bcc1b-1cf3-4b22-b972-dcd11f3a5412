# Gift管理器迁移指南

## 从依赖注入版本迁移到单例版本

### 涉及的类
1. **GiftCacheManager** → **GiftDownloadManager** (重命名 + 单例化)
2. **GiftManager** → **GiftManager** (单例化)

### 主要变化

#### 1. GiftDownloadManager (原GiftCacheManager) 变化
**原版本（依赖注入）：**
```kotlin
class GiftCacheManager @Inject constructor(
    @Dispatcher(AppDispatchers.IO) private val dispatchers: CoroutineDispatcher,
    @Dirs(AppDirs.CACHE) private val fileDir: File,
    @AppClient(AppClients.DOWNLOAD) private val networkService: NetworkServiceProvider,
    private val database: GiftDatabase,
)
```

**新版本（单例对象）：**
```kotlin
object GiftDownloadManager {
    private val dispatchers: CoroutineDispatcher = Dispatchers.IO
    private val fileDir: File by lazy { AppContext.context.cacheDir }
    private val okHttpClient: OkHttpClient by lazy { OkHttpClient.Builder().build() }
    private val database: GiftDatabase by lazy { GiftDatabase.getInstance(AppContext.context) }
}
```

#### 2. GiftManager 变化
**原版本（依赖注入）：**
```kotlin
@Singleton
class GiftManager @Inject constructor(
    @Dispatcher(AppDispatchers.IO) private val ioDispatcher: CoroutineDispatcher,
    private val giftCacheManager: GiftCacheManager,
    private val database: GiftDatabase
)
```

**新版本（单例）：**
```kotlin
class GiftManager private constructor() {
    companion object {
        @Volatile
        private var INSTANCE: GiftManager? = null

        fun getInstance(): GiftManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GiftManager().also { INSTANCE = it }
            }
        }
    }

    private val ioDispatcher: CoroutineDispatcher = Dispatchers.IO
    private val giftDownloadManager: GiftDownloadManager = GiftDownloadManager
    private val database: GiftDatabase by lazy { GiftDatabase.getInstance(AppContext.context) }
}
```

#### 2. 依赖管理变化
**原版本（依赖注入）：**
- 通过构造函数注入依赖
- 使用注解标记依赖类型
- 依赖框架管理生命周期

**新版本（单例）：**
```kotlin
// 手动管理的依赖
private val dispatchers: CoroutineDispatcher = Dispatchers.IO
private val fileDir: File by lazy { 
    AppContext.context.cacheDir
}
private val okHttpClient: OkHttpClient by lazy {
    OkHttpClient.Builder().build()
}
private val database: GiftDatabase by lazy {
    GiftDatabase.getInstance(AppContext.context)
}
```

#### 3. 网络请求实现变化
**原版本（依赖注入）：**
```kotlin
networkService[DownloadApiService::class.java].download(url).body()?.byteStream()
```

**新版本（单例）：**
```kotlin
val request = Request.Builder()
    .url(url)
    .build()

okHttpClient.newCall(request).execute().use { response ->
    if (response.isSuccessful) {
        response.body?.byteStream()?.use { input ->
            // 处理下载
        }
    }
}
```

### 使用方式变化

#### 原版本使用方式：
```kotlin
// 需要通过依赖注入框架获取实例
@Inject
lateinit var giftCacheManager: GiftCacheManager

@Inject
lateinit var giftManager: GiftManager

// 或者通过Hilt/Dagger获取
val giftCacheManager = hiltViewModel<SomeViewModel>().giftCacheManager
```

#### 新版本使用方式：
```kotlin
// GiftDownloadManager - 直接使用对象
val downloadManager = GiftDownloadManager
downloadManager.cacheGiftIfNeeded(giftInfo)

// GiftManager - 通过单例获取实例
val giftManager = GiftManager.getInstance()
giftManager.preloadGifts(giftList)
```

### 优势对比

#### 单例版本优势：
1. **简化依赖管理**：不需要配置复杂的依赖注入框架
2. **减少样板代码**：无需注解和模块配置
3. **更直观的使用方式**：直接调用getInstance()即可
4. **更好的控制**：手动管理依赖关系，更容易调试
5. **减少框架依赖**：不依赖Hilt/Dagger等框架

#### 依赖注入版本优势：
1. **更好的测试性**：容易进行单元测试和Mock
2. **更松耦合**：依赖关系更清晰
3. **更好的扩展性**：容易替换实现

### 注意事项

1. **线程安全**：单例实现使用了双重检查锁定模式，确保线程安全
2. **内存泄漏**：使用ApplicationContext避免内存泄漏
3. **懒加载**：使用lazy初始化减少启动时间
4. **异常处理**：保持原有的异常处理逻辑

### 迁移步骤

1. **移除依赖注入相关代码**
   - 删除所有 `@Inject` 注解
   - 删除 `@Singleton` 注解
   - 删除 `@Dispatcher` 等自定义注解

2. **更新类引用**
   - 将 `GiftCacheManager` 改为 `GiftDownloadManager`
   - 更新包名导入

3. **更新调用方式**
   - `GiftDownloadManager`: 直接使用对象 `GiftDownloadManager.cacheGiftIfNeeded()`
   - `GiftManager`: 使用单例 `GiftManager.getInstance().preloadGifts()`

4. **更新日志调用**
   - 将 `Timber` 改为 `Log` (Android标准日志)

5. **添加依赖**
   - 在 `lib_gift/build.gradle` 中添加 OkHttp 依赖

6. **测试功能**
   - 验证下载功能正常
   - 验证预加载功能正常

### 依赖添加

在 `lib_gift/build.gradle` 中添加：
```gradle
implementation 'com.squareup.okhttp3:okhttp:4.8.0'
```
