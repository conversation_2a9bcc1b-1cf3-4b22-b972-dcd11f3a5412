# GiftCacheManager 迁移指南

## 从依赖注入版本迁移到单例版本

### 主要变化

#### 1. 类声明变化
**原版本（依赖注入）：**
```kotlin
class GiftCacheManager @Inject constructor(
    @Dispatcher(AppDispatchers.IO) private val dispatchers: CoroutineDispatcher,
    @Dirs(AppDirs.CACHE) private val fileDir: File,
    @AppClient(AppClients.DOWNLOAD) private val networkService: NetworkServiceProvider,
    private val database: GiftDatabase,
)
```

**新版本（单例）：**
```kotlin
class GiftCacheManager private constructor() {
    companion object {
        @Volatile
        private var INSTANCE: GiftCacheManager? = null
        
        fun getInstance(): GiftCacheManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GiftCacheManager().also { INSTANCE = it }
            }
        }
    }
}
```

#### 2. 依赖管理变化
**原版本（依赖注入）：**
- 通过构造函数注入依赖
- 使用注解标记依赖类型
- 依赖框架管理生命周期

**新版本（单例）：**
```kotlin
// 手动管理的依赖
private val dispatchers: CoroutineDispatcher = Dispatchers.IO
private val fileDir: File by lazy { 
    AppContext.context.cacheDir
}
private val okHttpClient: OkHttpClient by lazy {
    OkHttpClient.Builder().build()
}
private val database: GiftDatabase by lazy {
    GiftDatabase.getInstance(AppContext.context)
}
```

#### 3. 网络请求实现变化
**原版本（依赖注入）：**
```kotlin
networkService[DownloadApiService::class.java].download(url).body()?.byteStream()
```

**新版本（单例）：**
```kotlin
val request = Request.Builder()
    .url(url)
    .build()

okHttpClient.newCall(request).execute().use { response ->
    if (response.isSuccessful) {
        response.body?.byteStream()?.use { input ->
            // 处理下载
        }
    }
}
```

### 使用方式变化

#### 原版本使用方式：
```kotlin
// 需要通过依赖注入框架获取实例
@Inject
lateinit var giftCacheManager: GiftCacheManager

// 或者通过Hilt/Dagger获取
val giftCacheManager = hiltViewModel<SomeViewModel>().giftCacheManager
```

#### 新版本使用方式：
```kotlin
// 直接通过单例获取实例
val giftCacheManager = GiftCacheManager.getInstance()

// 使用方法保持不变
giftCacheManager.cacheGiftIfNeeded(giftInfo)
```

### 优势对比

#### 单例版本优势：
1. **简化依赖管理**：不需要配置复杂的依赖注入框架
2. **减少样板代码**：无需注解和模块配置
3. **更直观的使用方式**：直接调用getInstance()即可
4. **更好的控制**：手动管理依赖关系，更容易调试
5. **减少框架依赖**：不依赖Hilt/Dagger等框架

#### 依赖注入版本优势：
1. **更好的测试性**：容易进行单元测试和Mock
2. **更松耦合**：依赖关系更清晰
3. **更好的扩展性**：容易替换实现

### 注意事项

1. **线程安全**：单例实现使用了双重检查锁定模式，确保线程安全
2. **内存泄漏**：使用ApplicationContext避免内存泄漏
3. **懒加载**：使用lazy初始化减少启动时间
4. **异常处理**：保持原有的异常处理逻辑

### 迁移步骤

1. 移除原有的依赖注入相关代码
2. 替换为新的单例实现
3. 更新调用方式从注入改为getInstance()
4. 添加OkHttp依赖到build.gradle
5. 测试功能是否正常工作

### 依赖添加

在 `lib_gift/build.gradle` 中添加：
```gradle
implementation 'com.squareup.okhttp3:okhttp:4.8.0'
```
